<?php

declare(strict_types=1);

return [
    // Common messages
    'success' => 'Success',
    'error_occurred' => 'Error occurred',
    'validation_failed' => 'Validation failed',
    'resource_not_found' => 'Resource not found',
    'unauthorized' => 'Unauthorized',

    // Authentication messages
    'auth' => [
        'login_success' => 'Login successful',
        'logout_success' => 'Logout successful',
        'all_tokens_revoked' => 'All tokens revoked',
        'invalid_credentials' => 'The provided credentials are incorrect.',
    ],

    // User messages
    'user' => [
        'list_retrieved' => 'Users retrieved successfully',
        'created' => 'User created successfully',
        'registered' => 'User registered successfully',
        'details_retrieved' => 'User details retrieved successfully',
        'updated' => 'User updated successfully',
        'suspended' => 'User suspended successfully',
        'activated' => 'User activated successfully',
        'not_found' => 'User not found',
        'added_to_organisation' => 'User added to organisation',
        'removed_from_organisation' => 'User removed from organisation',
        'organisations_synced' => 'User organisation associations synced',
        'verification_code_sent' => 'Verification code sent to your email address',
        'verification_code_sent_success' => 'Verification code sent successfully',
        'profile_updated' => 'Profile updated successfully',
        'password_changed' => 'Password changed successfully',
        'avatar_uploaded' => 'Avatar uploaded successfully',
    ],

    // Organisation messages
    'organisation' => [
        'list_retrieved' => 'Organisations retrieved successfully',
        'created' => 'Organisation created successfully',
        'details_retrieved' => 'Organisation details retrieved successfully',
        'updated' => 'Organisation updated successfully',
        'suspended' => 'Organisation suspended successfully',
        'already_suspended' => 'Organisation is already suspended',
    ],

    // Role messages
    'role' => [
        'list_retrieved' => 'Roles retrieved successfully',
        'created' => 'Role created successfully',
        'retrieved' => 'Role retrieved successfully',
        'updated' => 'Role updated successfully',
        'deleted' => 'Role deleted successfully',
        'assignment_success' => 'Role assigned successfully',
        'assignment_failed' => 'Role assignment failed',
        'removal_success' => 'Role removed successfully',
        'removal_failed' => 'Role removal failed',
        'transfer_success' => 'Owner role transferred successfully',
        'transfer_failed' => 'Owner role transfer failed',
        'assignable_list_retrieved' => 'Assignable roles list retrieved successfully',
        'user_roles_retrieved' => 'User role information retrieved successfully',
    ],

    // Invitation messages
    'invitation' => [
        'list_retrieved' => 'Invitations retrieved successfully',
        'created' => 'Invitation created successfully',
        'details_retrieved' => 'Invitation information retrieved successfully',
        'accepted' => 'Invitation accepted successfully, joined organization',
        'expired' => 'Invitation link has expired',
        'usage_limit_reached' => 'Invitation link has reached usage limit',
        'email_not_allowed' => 'Your email address is not allowed to accept this invitation',
        'processing_error' => 'Error processing invitation',
    ],

    // System messages
    'system' => [
        'api_running' => 'API is running successfully',
        'healthy' => 'healthy',
        'unhealthy' => 'unhealthy',
        'database_connection_success' => 'Database connection successful',
        'database_connection_failed' => 'Database connection failed',
        'cache_check_completed' => 'Cache check completed',
        'cache_check_failed' => 'Cache check failed',
        'storage_check_completed' => 'Storage check completed',
        'storage_check_failed' => 'Storage check failed',
    ],

    // Report messages
    'reports' => [
        'sales_retrieved' => 'Sales report retrieved successfully',
        'volume_retrieved' => 'Volume report retrieved successfully',
        'refunds_retrieved' => 'Refund report retrieved successfully',
        'order_status_retrieved' => 'Order status report retrieved successfully',
        'product_ranking_retrieved' => 'Product ranking report retrieved successfully',
        'daily_sales_overview_retrieved' => 'Daily sales overview retrieved successfully',
        'export_started' => 'Report export started successfully',
        'export_completed' => 'Report export completed successfully',
        'export_failed' => 'Report export failed',
        'no_data_found' => 'No data found for the specified criteria',
        'insufficient_permissions' => 'Insufficient permissions to access this report',
        'invalid_date_range' => 'Invalid date range specified',
        'data_processing_error' => 'Error occurred while processing report data',

        // Chart labels
        'chart_labels' => [
            'order_amount' => 'Order Amount',
            'order_quantity' => 'Order Quantity',
            'refund_amount' => 'Refund Amount',
            'refund_orders' => 'Refund Orders',
            'sales_amount' => 'Sales Amount',
            'quantity' => 'Quantity',
            'no_reason_provided' => 'No reason provided',
        ],

        // Order status labels
        'order_status' => [
            'completed' => 'Completed',
            'processing' => 'Processing',
            'cancelled' => 'Cancelled',
            'pending' => 'Pending',
        ],

        // Payment status labels
        'payment_status' => [
            'completed' => 'Paid',
            'pending' => 'Pending Payment',
            'failed' => 'Payment Failed',
            'cancelled' => 'Cancelled',
        ],

        // Region labels
        'regions' => [
            'US' => 'North America',
            'CA' => 'North America',
            'MX' => 'North America',
            'GB' => 'Europe',
            'DE' => 'Europe',
            'FR' => 'Europe',
            'IT' => 'Europe',
            'ES' => 'Europe',
            'CN' => 'Asia',
            'JP' => 'Asia',
            'KR' => 'Asia',
            'IN' => 'Asia',
            'SG' => 'Asia',
            'other' => 'Other',
        ],
    ],

    // Validation messages
    'validation' => [
        // Common validation messages
        'required' => 'This field is required',
        'string' => 'This field must be a string',
        'email' => 'Please enter a valid email address',
        'unique' => 'This value is already in use',
        'integer' => 'This field must be an integer',
        'array' => 'This field must be an array',
        'exists' => 'The selected value does not exist',
        'date' => 'This field must be a valid date',
        'after' => 'This field must be a date after now',
        'in' => 'The selected value is invalid',
        'size' => 'This field must be exactly :size characters',
        'min' => 'This field must be at least :min characters',
        'max' => 'This field cannot exceed :max characters',

        // User validation messages
        'user' => [
            'name_required' => 'Name is required',
            'name_string' => 'Name must be a string',
            'name_max' => 'Name cannot exceed 255 characters',
            'email_required' => 'Email address is required',
            'email_format' => 'Email address format is invalid',
            'email_unique' => 'This email address is already in use',
            'email_max' => 'Email address cannot exceed 255 characters',
            'password_required' => 'Password is required',
            'password_string' => 'Password must be a string',
            'password_min' => 'Password must be at least 8 characters',
            'password_max' => 'Password cannot exceed 255 characters',
            'organisation_ids_array' => 'Organization ID list must be an array',
            'organisation_ids_integer' => 'Organization ID must be an integer',
            'organisation_ids_exists' => 'The specified organization does not exist',
            'verification_code_required' => 'Verification code is required',
            'verification_code_string' => 'Verification code must be a string',
            'verification_code_size' => 'Verification code must be exactly 6 characters',
            'verification_code_invalid' => 'Invalid or expired verification code',
            'organisation_ids_not_allowed' => 'Organization assignment is not allowed for guest registration',
        ],

        // Organisation validation messages
        'organisation' => [
            'name_required' => 'Organization name is required',
            'name_string' => 'Organization name must be a string',
            'name_max' => 'Organization name cannot exceed 255 characters',
            'code_required' => 'Organization code is required',
            'code_string' => 'Organization code must be a string',
            'code_max' => 'Organization code cannot exceed 50 characters',
            'code_unique' => 'Organization code already exists',
            'details_array' => 'Details must be in array format',
            'remarks_string' => 'Remarks must be a string',
            'remarks_max' => 'Remarks cannot exceed 1000 characters',
            'status_string' => 'Status must be a string',
            'status_in' => 'Status must be one of pending, active, or suspended',
        ],

        // Invitation validation messages
        'invitation' => [
            'model_type_required' => 'Model type is required',
            'model_type_in' => 'Model type must be a valid organization',
            'model_id_required' => 'Organization ID is required',
            'model_id_exists' => 'Organization does not exist',
            'role_required' => 'Role is required',
            'role_in' => 'Role must be either owner or member',
            'expires_at_date' => 'Expiration date must be a valid date',
            'expires_at_after' => 'Expiration date must be in the future',
            'max_uses_integer' => 'Maximum uses must be a number',
            'max_uses_min' => 'Maximum uses must be at least 1',
            'max_uses_max' => 'Maximum uses cannot exceed 100',
            'email_restriction_email' => 'Email restriction must be a valid email address',
        ],

        // Invitation attributes
        'invitation_attributes' => [
            'model_type' => 'Model Type',
            'model_id' => 'Organization ID',
            'role' => 'Role',
            'expires_at' => 'Expiration Date',
            'max_uses' => 'Maximum Uses',
            'email_restriction' => 'Email Restriction',
        ],

        // Report validation messages
        'reports' => [
            'start_date_required' => 'Start date is required',
            'end_date_required' => 'End date is required',
            'invalid_date_range' => 'Start date must be before or equal to end date',
            'end_date_future' => 'End date cannot be in the future',
            'invalid_country_code' => 'Country code must be 2 characters',
            'invalid_group_by' => 'Group by must be one of: hour, day, week, month, quarter, year',
            'invalid_state' => 'Invalid order state',
            'invalid_payment_state' => 'Invalid payment state',
            'limit_integer' => 'Limit must be an integer',
            'limit_min' => 'Limit must be at least 1',
            'limit_max' => 'Limit cannot exceed 100',
            'invalid_organisation' => 'Organization does not exist',
            'organisation_ids_required' => 'Organisation IDs are required',
            'organisation_ids_must_be_array' => 'Organisation IDs must be an array',
            'organisation_ids_min_one' => 'At least one organisation ID is required',
            'invalid_currency_code' => 'Currency code must be 3 characters',
            'invalid_timezone' => 'Invalid timezone',
            'report_type_required' => 'Report type is required',
            'invalid_report_type' => 'Report type must be one of: sales, volume, refunds, order_status',
            'format_required' => 'Export format is required',
            'invalid_format' => 'Export format must be one of: xlsx, csv, pdf',
            'invalid_filename' => 'Filename contains invalid characters',
            'filename_too_long' => 'Filename cannot exceed 255 characters',
            'max_records_exceeded' => 'Maximum records limit exceeded (50,000)',
            'min_records_required' => 'At least 1 record is required',
            'invalid_email' => 'Invalid email address',
        ],

        // Report attributes
        'attributes' => [
            'start_date' => 'Start Date',
            'end_date' => 'End Date',
            'group_by' => 'Group By',
            'countries' => 'Countries',
            'states' => 'Order States',
            'payment_states' => 'Payment States',
            'organisation_ids' => 'Organizations',
            'currency' => 'Currency',
            'timezone' => 'Timezone',
            'include_refunds' => 'Include Refunds',
            'refund_status' => 'Refund Status',
            'report_type' => 'Report Type',
            'format' => 'Export Format',
            'filename' => 'Filename',
            'include_summary' => 'Include Summary',
            'include_charts' => 'Include Charts',
            'max_records' => 'Maximum Records',
            'email_to' => 'Email To',
            'async' => 'Asynchronous Processing',
        ],
    ],
];
