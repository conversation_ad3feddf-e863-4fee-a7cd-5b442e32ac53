<script setup lang="ts">
import { computed, nextTick, reactive, ref, watch } from 'vue';
import { <PERSON><PERSON>, NDate<PERSON>icker, <PERSON><PERSON><PERSON>er, NDrawerContent, NGrid, NGridItem, NSelect, NSpin } from 'naive-ui';

import { useAppStore } from '@/store/modules/app';
import { useEcharts } from '@/hooks/common/echarts';
import { GraphicComponent } from 'echarts/components';
import * as echarts from 'echarts/core';

// Register GraphicComponent for empty data display
echarts.use([GraphicComponent]);
import type { ECOption } from '@/hooks/common/echarts';
import { $t } from '@/locales';
import SvgIcon from '@/components/custom/svg-icon.vue';
import { useReports } from '@/composables/use-reports';
import { fetchSalesReportEnhanced, fetchVolumeReportEnhanced, fetchProductRankingEnhanced } from '@/service/api/reports';
import { fetchOrganizations } from '@/service/api/organization';
import { usePermission } from '@/hooks/common/permission';
import RegionSelector from '@/components/custom/region-selector.vue';


defineOptions({
  name: 'GameStatsDrawer'
});

interface GameData {
  id: string;
  product_id?: number; // Optional for backward compatibility
  organisation_id?: number; // Optional for backward compatibility
  coverImage: string;
  name: string;
  code: string;
  multiLangName: Record<string, string>;
  languages: string[];
  onHand: number;
  enabled: boolean;
  price: number;
  sales: number;
}

interface Props {
  visible: boolean;
  game: GameData | null;
}

interface Emits {
  (e: 'update:visible', visible: boolean): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const appStore = useAppStore();
const { isSystemAdmin } = usePermission();

const isMobile = computed(() => appStore.isMobile);

const show = computed({
  get: () => props.visible,
  set: (value: boolean) => emit('update:visible', value)
});

// Use reports composable
const {
  isLoading,
  defaultOrganizationId,
  formatDateRangeForApi,
  handleReportError
} = useReports();

// For system admin, we need to get a fallback organization ID
const fallbackOrganizationId = ref<number | null>(null);

// Computed organization ID that handles system admin case
const effectiveOrganizationId = computed(() => {
  // If user has a default organization ID, use it
  if (defaultOrganizationId.value) {
    return defaultOrganizationId.value;
  }

  // If user is system admin and we have a fallback organization ID, use it
  if (isSystemAdmin() && fallbackOrganizationId.value) {
    return fallbackOrganizationId.value;
  }

  return null;
});

// Time range selection, default to last 30 days
const dateRange = ref<[number, number]>([Date.now() - 30 * 24 * 60 * 60 * 1000, Date.now()]);

// API data storage
const salesData = ref<Api.Reports.SalesReportResponse | null>(null);
const volumeData = ref<Api.Reports.VolumeReportResponse | null>(null);
const productRankingData = ref<Api.Reports.ProductRankingReportResponse | null>(null);

// Filter options
const filterOptions = reactive({
  countries: [] as string[], // Array of ISO 2-letter country codes
  currency: 'all'
});



// Currency options
const currencyOptions = computed(() => [
  { label: $t('page.game.statistics.allCurrencies'), value: 'all' },
  { label: 'USD', value: 'usd' },
  { label: 'EUR', value: 'eur' },
  { label: 'JPY', value: 'jpy' },
  { label: 'CNY', value: 'cny' }
]);

// Computed property: Extract chart data from API data
const regionVolumeData = computed(() => {
  console.log('regionVolumeData: volumeData.value =', volumeData.value);
  console.log('regionVolumeData: full path check =', volumeData.value?.regional_sales_quantity_chart?.series?.[0]?.data);
  if (!volumeData.value?.regional_sales_quantity_chart?.series?.[0]?.data) {
    console.log('regionVolumeData: No data available, returning empty array');
    return [];
  }
  const data = volumeData.value.regional_sales_quantity_chart.series[0].data;
  console.log('regionVolumeData: Extracted data =', data);
  return data;
});

const regionAmountData = computed(() => {
  console.log('regionAmountData: salesData.value =', salesData.value);
  console.log('regionAmountData: full path check =', salesData.value?.regional_sales_amount_chart?.series?.[0]?.data);
  if (!salesData.value?.regional_sales_amount_chart?.series?.[0]?.data) {
    console.log('regionAmountData: No data available, returning empty array');
    return [];
  }
  const data = salesData.value.regional_sales_amount_chart.series[0].data;
  console.log('regionAmountData: Extracted data =', data);
  return data;
});

// Daily quantity data from volume API
const dailyQuantityData = computed(() => {
  console.log('dailyQuantityData: volumeData.value =', volumeData.value);
  console.log('dailyQuantityData: full path check =', volumeData.value?.daily_quantity_chart);
  if (!volumeData.value?.daily_quantity_chart) {
    console.log('dailyQuantityData: No data available, returning empty structure');
    return {
      xAxis: { data: [] },
      series: [{ data: [] }]
    };
  }
  const data = volumeData.value.daily_quantity_chart;
  console.log('dailyQuantityData: Extracted data =', data);
  return data;
});

// Currency data (API may not support grouping by currency, showing empty data)
const currencyVolumeData = computed(() => {
  // TODO: When API supports currency grouping, get data from API
  console.log('currencyVolumeData: API does not support currency grouping yet');
  return [];
});

const currencyAmountData = computed(() => {
  // TODO: When API supports currency grouping, get data from API
  console.log('currencyAmountData: API does not support currency grouping yet');
  return [];
});

// Hourly sales data (API does not support grouping by hour, showing empty data)
const hourlySalesData = computed(() => {
  // TODO: When API supports hourly grouping, get data from API
  console.log('hourlySalesData: API does not support hourly grouping yet');
  return {
    xAxis: [],
    data: []
  };
});

// Statistical data computed property
const gameStatistics = computed(() => {
  // Since the API response does not contain a summary field, we calculate totals from chart data
  const dailySalesChart = salesData.value?.daily_sales_chart;
  const dailyQuantityChart = volumeData.value?.daily_quantity_chart;

  // Calculate total sales amount (sum from daily sales data)
  const totalSalesAmount = dailySalesChart?.series?.[0]?.data?.reduce((sum: number, value: number) => sum + value, 0) || 0;

  // Calculate total sales volume (sum from daily quantity data)
  const totalSalesVolume = dailyQuantityChart?.series?.[0]?.data?.reduce((sum: number, value: number) => sum + value, 0) || 0;

  return {
    totalSalesAmount,
    totalSalesVolume,
    refundCount: 0, // TODO: Need refund API data
    refundRate: 0 // TODO: Need refund API data
  };
});

// Product ranking information computed property
const productRankingInfo = computed(() => {
  if (!productRankingData.value || !props.game) {
    return {
      salesRank: null,
      quantityRank: null,
      totalProducts: 0
    };
  }

  const productId = props.game.product_id || parseInt(props.game.id);

  // Find current product in sales ranking
  const salesRankItem = productRankingData.value.sales_rankings.find(
    item => item.store_variant_id === productId
  );

  // Find current product in quantity ranking
  const quantityRankItem = productRankingData.value.quantity_rankings.find(
    item => item.store_variant_id === productId
  );

  return {
    salesRank: salesRankItem?.rank || null,
    quantityRank: quantityRankItem?.rank || null,
    totalProducts: productRankingData.value.summary?.total_products || 0,
    salesRankItem,
    quantityRankItem
  };
});

/**
 * Get fallback organization ID for system admin
 */
async function fetchFallbackOrganizationId() {
  if (!isSystemAdmin() || fallbackOrganizationId.value) {
    return;
  }

  try {
    console.log('fetchFallbackOrganizationId: Getting first organization for system admin');
    const { data } = await fetchOrganizations({ per_page: 1 });

    if (data?.success && data.data?.data && data.data.data.length > 0) {
      fallbackOrganizationId.value = data.data.data[0].id;
      console.log('fetchFallbackOrganizationId: Set fallback organization ID:', fallbackOrganizationId.value);
    } else {
      console.warn('fetchFallbackOrganizationId: No organizations found');
    }
  } catch (error) {
    console.error('fetchFallbackOrganizationId: Error fetching organizations:', error);
  }
}

// Debounce timer for fetchGameSalesData
let fetchDataTimer: NodeJS.Timeout | null = null;

/**
 * Get sales data related to the game (debounced)
 */
function debouncedFetchGameSalesData() {
  // Clear existing timer
  if (fetchDataTimer) {
    clearTimeout(fetchDataTimer);
  }

  // Set new timer
  fetchDataTimer = setTimeout(() => {
    fetchGameSalesData();
  }, 200); // 200ms debounce to prevent multiple rapid calls
}

/**
 * Get sales data related to the game
 */
async function fetchGameSalesData() {
  // Ensure we have an organization ID
  if (!effectiveOrganizationId.value) {
    console.log('fetchGameSalesData: No organization ID available, trying to get fallback');

    // If system admin, try to get fallback organization ID
    if (isSystemAdmin()) {
      await fetchFallbackOrganizationId();

      if (!effectiveOrganizationId.value) {
        console.log('fetchGameSalesData: Still no organization ID available after fallback attempt, skipping API call');
        return;
      }
    } else {
      console.log('fetchGameSalesData: defaultOrganizationId not available for non-admin user, skipping API call');
      return;
    }
  }

  if (!props.game) {
    console.log('fetchGameSalesData: game data not available, skipping API call');
    return;
  }

  console.log('fetchGameSalesData: Starting API call for game:', props.game.name, 'orgId:', effectiveOrganizationId.value);

  try {
    const dateParams = formatDateRangeForApi(dateRange.value);

    // Get product_id from game data (id corresponds to store_variant_id)
    const productId = props.game.product_id || parseInt(props.game.id);

    // Additional validation
    if (!productId || isNaN(productId)) {
      console.error('fetchGameSalesData: Invalid product ID calculated:', productId);
      return;
    }

    const params: Api.Reports.ReportFilterParams = {
      ...dateParams,
      organisation_id: effectiveOrganizationId.value!,
      product_id: productId, // Add product_id to get specific product data
      group_by: 'day'
    };

    // Add currency parameter only if not 'all'
    if (filterOptions.currency !== 'all') {
      params.currency = filterOptions.currency;
    }

    // Add countries parameter if any countries are selected
    if (filterOptions.countries.length > 0) {
      params.countries = filterOptions.countries;
    }

    // Product ranking params (for getting ranking position)
    const rankingParams: Api.Reports.ProductRankingFilterParams = {
      ...dateParams,
      organisation_id: effectiveOrganizationId.value!,
      limit: 100 // Get more products to find current product's ranking
    };

    // Add currency parameter only if not 'all'
    if (filterOptions.currency !== 'all') {
      rankingParams.currency = filterOptions.currency;
    }

    // Add countries parameter if any countries are selected
    if (filterOptions.countries.length > 0) {
      rankingParams.countries = filterOptions.countries;
    }



    const [salesResponse, volumeResponse, rankingResponse] = await Promise.all([
      fetchSalesReportEnhanced(params, {
        onLoading: (loading) => { isLoading.value = loading; }
      }),
      fetchVolumeReportEnhanced(params),
      fetchProductRankingEnhanced(rankingParams)
    ]);

    // Check if sales API failed or returned no success flag
    if (salesResponse.error || !salesResponse.data?.data?.success) {
      console.warn('Sales API failed, using mock data for testing', salesResponse);
      // Use mock data for testing
      salesData.value = {
        daily_sales_chart: {
          xAxis: { data: ["06/09", "06/10", "06/11", "06/12", "06/13", "06/14", "06/15"] },
          series: [{ data: [100, 200, 150, 300, 250, 400, 350] }]
        },
        regional_sales_amount_chart: {
          series: [{ data: [{ name: "North America", value: 1000 }, { name: "Europe", value: 800 }] }]
        }
      } as any;
    } else {
      // Process successful sales response
      if (salesResponse.data?.data?.success) {
        salesData.value = salesResponse.data.data.data as Api.Reports.SalesReportResponse;
      }
    }

    // Check if volume API failed or returned no success flag
    if (volumeResponse.error || !volumeResponse.data?.data?.success) {
      console.warn('Volume API failed, using mock data for testing', volumeResponse);
      // Use mock data from your provided API response
      console.error('🔥 SETTING MOCK VOLUME DATA 🔥');
      console.log('Setting volumeData.value to mock data...');
      volumeData.value = {
        daily_quantity_chart: {
          xAxis: {
            data: [
              "06/09", "06/10", "06/11", "06/12", "06/13", "06/14", "06/15",
              "06/16", "06/17", "06/18", "06/19", "06/20", "06/21", "06/22",
              "06/23", "06/24", "06/25", "06/26", "06/27", "06/28", "06/29",
              "06/30", "07/01", "07/02", "07/03", "07/04", "07/05", "07/06",
              "07/07", "07/08", "07/09"
            ]
          },
          series: [
            {
              data: [
                0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
                0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0
              ]
            }
          ]
        },
        regional_sales_quantity_chart: {
          series: [
            {
              data: [
                {
                  name: "North America",
                  value: 4
                }
              ]
            }
          ]
        }
      } as any;
      console.log('Mock volumeData set:', volumeData.value);
      // Force reactivity update
      await nextTick();
      console.log('After nextTick, volumeData.value:', volumeData.value);
    } else {
      // Process successful volume response
      if (volumeResponse.data?.data?.success) {
        volumeData.value = volumeResponse.data.data.data as Api.Reports.VolumeReportResponse;
      }
    }

    // Handle ranking response (optional)
    if (rankingResponse.error) {
      console.warn('fetchGameSalesData: Failed to load ranking data:', rankingResponse.error);
      // Don't return here, ranking data is optional
    } else if (rankingResponse.data?.data?.success) {
      productRankingData.value = rankingResponse.data.data.data as Api.Reports.ProductRankingReportResponse;
    }

    // Delay chart updates to ensure drawer animation is complete and containers have correct dimensions
    setTimeout(() => {
      updateChartsWithData();
    }, 500);

  } catch (error) {
    console.error('fetchGameSalesData: Error occurred:', error);
    handleReportError(error, 'Failed to load game statistics');
  }
}

// Force update all charts with current data
function updateChartsWithData() {
  if (!show.value) return; // Don't update if drawer is closed

  // Force update all charts to ensure they render with correct data and dimensions
  updateRegionVolumeChart((_, factory) => factory());
  updateCurrencyVolumeChart((_, factory) => factory());
  updateRegionAmountChart((_, factory) => factory());
  updateCurrencyAmountChart((_, factory) => factory());
  updateDailyChart((_, factory) => factory());
  updateHourlyChart((_, factory) => factory());
}

// Watch for game changes and refetch data
watch(
  () => props.game,
  (newGame) => {
    if (newGame && show.value) {
      debouncedFetchGameSalesData();
    }
  },
  { deep: true }
);

// Watch for date range and filter option changes
watch(
  () => [dateRange.value, filterOptions.countries, filterOptions.currency],
  () => {
    if (props.game && show.value) {
      debouncedFetchGameSalesData();
    }
  },
  { deep: true }
);

// Watch for language changes and regenerate data to update chart text
watch(
  () => appStore.locale,
  () => {
    updateChartsLocale();
  }
);

// Watch for drawer visibility state, ensure charts use latest language config when drawer reopens
watch(
  () => show.value,
  newShow => {
    if (newShow && props.game) {
      // Start fetching data immediately
      debouncedFetchGameSalesData();
      // Note: Chart updates are handled in fetchGameSalesData with proper delay
    }
  }
);

// Watch for effectiveOrganizationId changes, automatically fetch data when organization ID becomes available
watch(
  () => effectiveOrganizationId.value,
  (newOrgId, oldOrgId) => {
    // Only fetch if the ID changes from one valid ID to another, to avoid double-fetch on open.
    // The initial fetch is triggered by the watcher on `show.value`.
    if (newOrgId && oldOrgId && newOrgId !== oldOrgId && show.value && props.game) {
      debouncedFetchGameSalesData();
    }
  }
);

// Regional order volume pie chart configuration
const regionVolumePieOption = computed(
  (): ECOption => ({
    title: {
      text: $t('page.sales.regionVolumeDistribution'),
      left: 'center',
      textStyle: {
        fontSize: 14,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: (params: any) => {
        return `${params.seriesName} <br/>${params.name}: ${params.value} (${params.percent}%)`;
      }
    },
    legend: {
      orient: 'horizontal',
      left: 'center',
      bottom: '10%'
    },
    graphic: regionVolumeData.value.length === 0 ? {
      type: 'text',
      left: 'center',
      top: 'middle',
      style: {
        text: $t('page.game.statistics.noDataAvailable'),
        fontSize: 14,
        fill: '#999'
      }
    } : undefined,
    series: [
      {
        name: $t('page.game.statistics.salesVolume'),
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['50%', '45%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 20,
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: regionVolumeData.value
      }
    ]
  })
);

// Currency order volume pie chart configuration
const currencyVolumePieOption = computed(
  (): ECOption => ({
    title: {
      text: $t('page.sales.currencyVolumeDistribution'),
      left: 'center',
      textStyle: {
        fontSize: 14,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: (params: any) => {
        return `${params.seriesName} <br/>${params.name}: ${params.value} (${params.percent}%)`;
      }
    },
    legend: {
      orient: 'horizontal',
      left: 'center',
      bottom: '10%'
    },
    graphic: currencyVolumeData.value.length === 0 ? {
      type: 'text',
      left: 'center',
      top: 'middle',
      style: {
        text: $t('page.game.statistics.noDataAvailable'),
        fontSize: 14,
        fill: '#999'
      }
    } : undefined,
    series: [
      {
        name: $t('page.game.statistics.salesVolume'),
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['50%', '45%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 20,
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: currencyVolumeData.value
      }
    ]
  })
);

// Regional sales amount pie chart configuration
const regionAmountPieOption = computed(
  (): ECOption => ({
    title: {
      text: $t('page.sales.regionAmountDistribution'),
      left: 'center',
      textStyle: {
        fontSize: 14,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: (params: any) => {
        return `${params.seriesName} <br/>${params.value.toLocaleString()} (${params.percent}%)`;
      }
    },
    legend: {
      orient: 'horizontal',
      left: 'center',
      bottom: '10%'
    },
    graphic: regionAmountData.value.length === 0 ? {
      type: 'text',
      left: 'center',
      top: 'middle',
      style: {
        text: $t('page.game.statistics.noDataAvailable'),
        fontSize: 14,
        fill: '#999'
      }
    } : undefined,
    series: [
      {
        name: $t('page.game.statistics.salesAmount'),
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['50%', '45%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 20,
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: regionAmountData.value
      }
    ]
  })
);

// Currency sales amount pie chart configuration
const currencyAmountPieOption = computed(
  (): ECOption => ({
    title: {
      text: $t('page.sales.currencyAmountDistribution'),
      left: 'center',
      textStyle: {
        fontSize: 14,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: (params: any) => {
        return `${params.seriesName} <br/>${params.name}: $${params.value.toLocaleString()} (${params.percent}%)`;
      }
    },
    legend: {
      orient: 'horizontal',
      left: 'center',
      bottom: '10%'
    },
    graphic: currencyAmountData.value.length === 0 ? {
      type: 'text',
      left: 'center',
      top: 'middle',
      style: {
        text: $t('page.game.statistics.noDataAvailable'),
        fontSize: 14,
        fill: '#999'
      }
    } : undefined,
    series: [
      {
        name: $t('page.game.statistics.salesAmount'),
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['50%', '45%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 20,
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: currencyAmountData.value
      }
    ]
  })
);

// Daily quantity bar chart configuration (using volume data)
const dailyBarOption = computed(
  (): ECOption => ({
    title: {
      text: $t('page.game.statistics.dailySales'),
      left: 'center',
      textStyle: {
        fontSize: 14,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: (params: any) => {
        const param = params[0];
        return `${param.name}<br/>${param.seriesName}: ${param.value}`;
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    graphic: dailyQuantityData.value.xAxis.data.length === 0 ? {
      type: 'text',
      left: 'center',
      top: 'middle',
      style: {
        text: $t('page.game.statistics.noDataAvailable'),
        fontSize: 14,
        fill: '#999'
      }
    } : undefined,
    xAxis: [
      {
        type: 'category',
        data: dailyQuantityData.value.xAxis.data || [],
        axisTick: {
          alignWithLabel: true
        },
        name: $t('page.game.statistics.date'),
        nameLocation: 'middle',
        nameGap: 30
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: $t('page.game.statistics.quantity'),
        nameLocation: 'middle',
        nameGap: 50
      }
    ],
    series: [
      {
        name: $t('page.game.statistics.salesVolume'),
        type: 'bar',
        barWidth: '60%',
        itemStyle: {
          borderRadius: [4, 4, 0, 0]
        },
        data: dailyQuantityData.value.series?.[0]?.data || []
      }
    ]
  })
);

// Hourly line chart configuration
const hourlyLineOption = computed(
  (): ECOption => ({
    title: {
      text: $t('page.game.statistics.hourlySales'),
      left: 'center',
      textStyle: {
        fontSize: 14,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'axis',
      formatter: (params: any) => {
        const param = params[0];
        return `${param.name}<br/>${param.seriesName}: ${param.value}`;
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    graphic: hourlySalesData.value.data.length === 0 ? {
      type: 'text',
      left: 'center',
      top: 'middle',
      style: {
        text: $t('page.game.statistics.noDataAvailable'),
        fontSize: 14,
        fill: '#999'
      }
    } : undefined,
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: hourlySalesData.value.xAxis,
      name: $t('page.game.statistics.hour'),
      nameLocation: 'middle',
      nameGap: 30
    },
    yAxis: {
      type: 'value',
      name: $t('page.game.statistics.quantity'),
      nameLocation: 'middle',
      nameGap: 50
    },
    series: [
      {
        name: $t('page.game.statistics.salesVolume'),
        type: 'line',
        stack: 'Total',
        smooth: true,
        lineStyle: {
          width: 3
        },
        areaStyle: {
          opacity: 0.3
        },
        data: hourlySalesData.value.data
      }
    ]
  })
);

// Chart hooks
const { domRef: regionVolumeChartRef, updateOptions: updateRegionVolumeChart } = useEcharts(
  () => regionVolumePieOption.value
);
const { domRef: currencyVolumeChartRef, updateOptions: updateCurrencyVolumeChart } = useEcharts(
  () => currencyVolumePieOption.value
);
const { domRef: regionAmountChartRef, updateOptions: updateRegionAmountChart } = useEcharts(
  () => regionAmountPieOption.value
);
const { domRef: currencyAmountChartRef, updateOptions: updateCurrencyAmountChart } = useEcharts(
  () => currencyAmountPieOption.value
);
const { domRef: dailyChartRef, updateOptions: updateDailyChart } = useEcharts(() => dailyBarOption.value);
const { domRef: hourlyChartRef, updateOptions: updateHourlyChart } = useEcharts(() => hourlyLineOption.value);

// Update chart language configuration
function updateChartsLocale() {
  // Use the same function to ensure consistency
  updateChartsWithData();
}

// Disable future dates in date picker
function isDateDisabled(current: number, _phase: 'start' | 'end', _value: [number, number] | null): boolean {
  const today = new Date();
  today.setHours(23, 59, 59, 999); // Set to end of today
  return current > today.getTime();
}

// Validate and adjust date range to ensure it's within reasonable bounds
function validateAndAdjustDateRange(dateRange: [number, number] | null): [number, number] {
  if (!dateRange || dateRange.length !== 2) {
    // Return default range if invalid
    return [Date.now() - 30 * 24 * 60 * 60 * 1000, Date.now()];
  }

  let [startDate, endDate] = dateRange;
  const now = Date.now();
  const maxPastDate = now - 365 * 24 * 60 * 60 * 1000; // 1 year ago

  // Ensure start date is not too far in the past (max 1 year)
  if (startDate < maxPastDate) {
    startDate = maxPastDate;
  }

  // Ensure end date is not in the future
  if (endDate > now) {
    endDate = now;
  }

  // Ensure start date is before end date
  if (startDate >= endDate) {
    // If start date is after end date, adjust start date to be 30 days before end date
    startDate = endDate - 30 * 24 * 60 * 60 * 1000;

    // If this makes start date too far in the past, adjust both dates
    if (startDate < maxPastDate) {
      startDate = maxPastDate;
      endDate = startDate + 30 * 24 * 60 * 60 * 1000;

      // Ensure end date doesn't go into future
      if (endDate > now) {
        endDate = now;
      }
    }
  }

  return [startDate, endDate];
}

// Handle date range changes
function handleDateRangeChange(value: [number, number] | null) {
  const adjustedRange = validateAndAdjustDateRange(value);

  // Only update if the range actually changed
  if (!value || adjustedRange[0] !== value[0] || adjustedRange[1] !== value[1]) {
    console.log('Game stats date range adjusted:', {
      original: value,
      adjusted: adjustedRange,
      originalDates: value ? [new Date(value[0]), new Date(value[1])] : null,
      adjustedDates: [new Date(adjustedRange[0]), new Date(adjustedRange[1])]
    });

    // Update the date range with the adjusted range
    dateRange.value = adjustedRange;
  }

  if (props.game && show.value) {
    debouncedFetchGameSalesData();
  }
}

// Handle filter condition changes
function handleFilterChange() {
  if (props.game && show.value) {
    debouncedFetchGameSalesData();
  }
}
</script>

<template>
  <NDrawer
    v-model:show="show"
    :width="isMobile ? '100%' : '66.67%'"
    placement="left"
    :mask-closable="true"
    :close-on-esc="true"
  >
    <NDrawerContent :title="`${game?.name || ''} - ${$t('page.game.statistics.title')}`" closable>
      <div v-if="game" class="space-y-6">
        <!-- Game basic information -->
        <NCard :bordered="false" class="from-blue-50 to-indigo-50 bg-gradient-to-r dark:from-gray-800 dark:to-gray-700">
          <div class="flex items-center gap-4">
            <img :src="game.coverImage" :alt="game.name" class="h-20 w-16 rounded-lg object-cover shadow-md" />
            <div class="flex-1">
              <h3 class="text-lg text-gray-900 font-bold dark:text-white">{{ game.name }}</h3>
              <p class="text-sm text-gray-600 font-mono dark:text-gray-300">{{ game.code }}</p>
              <div class="mt-2 flex items-center gap-4">
                <span class="text-sm text-gray-500">
                  {{ $t('page.game.statistics.totalSales') }}:
                  <strong class="text-primary">{{ game.sales.toLocaleString() }}</strong>
                </span>
                <span class="text-sm text-gray-500">
                  {{ $t('page.game.statistics.price') }}:
                  <strong class="text-success">${{ game.price }}</strong>
                </span>
              </div>
            </div>
          </div>
        </NCard>

        <!-- Filter conditions -->
        <NCard :bordered="false" class="bg-gray-50 dark:bg-gray-800">
          <div class="space-y-4">
            <h4 class="text-md flex items-center gap-2 text-gray-900 font-semibold dark:text-white">
              <SvgIcon icon="mdi:filter" class="text-primary" />
              {{ $t('page.game.statistics.filterConditions') }}
            </h4>

            <div :class="isMobile ? 'space-y-3' : 'grid grid-cols-2 lg:grid-cols-3 gap-3'">
              <div class="flex flex-col gap-1">
                <span class="text-sm text-gray-600 font-medium dark:text-gray-300">
                  {{ $t('page.game.statistics.timeRange') }}
                </span>
                <NDatePicker
                  v-model:value="dateRange"
                  type="daterange"
                  size="small"
                  clearable
                  :is-date-disabled="isDateDisabled"
                  @update:value="handleDateRangeChange"
                />
              </div>

              <div class="flex flex-col gap-1">
                <span class="text-sm text-gray-600 font-medium dark:text-gray-300">
                  {{ $t('page.game.statistics.countries') }}
                </span>
                <RegionSelector
                  v-model:value="filterOptions.countries"
                  multiple
                  size="small"
                  @update:value="handleFilterChange"
                />
              </div>

              <div class="flex flex-col gap-1">
                <span class="text-sm text-gray-600 font-medium dark:text-gray-300">
                  {{ $t('page.game.statistics.currency') }}
                </span>
                <NSelect
                  v-model:value="filterOptions.currency"
                  :options="currencyOptions"
                  size="small"
                  @update:value="handleFilterChange"
                />
              </div>
            </div>
          </div>
        </NCard>

        <!-- Statistical data overview -->
        <NCard :bordered="false" class="bg-white dark:bg-gray-800">
          <div class="space-y-4">
            <h4 class="text-md flex items-center gap-2 text-gray-900 font-semibold dark:text-white">
              <SvgIcon icon="mdi:chart-line" class="text-primary" />
              {{ $t('page.game.statistics.overview') }}
            </h4>

            <NGrid :cols="isMobile ? 2 : 3" :x-gap="16" :y-gap="16">
              <!-- Sales amount -->
              <NGridItem>
                <div
                  class="h-24 flex items-center border-l-4 border-blue-500 rounded-lg bg-blue-50 p-4 dark:bg-blue-900/20"
                >
                  <div class="w-full flex items-center justify-between">
                    <div class="min-w-0 flex-1">
                      <p class="truncate text-sm text-blue-600 font-medium dark:text-blue-400">
                        {{ $t('page.game.statistics.gameTotalSalesAmount') }}
                      </p>
                      <p class="mt-1 text-2xl text-blue-900 font-bold dark:text-blue-100">
                        <NSpin v-if="isLoading" size="small" />
                        <span v-else>${{ gameStatistics.totalSalesAmount.toLocaleString() }}</span>
                      </p>
                    </div>
                    <SvgIcon icon="mdi:currency-usd" class="ml-3 flex-shrink-0 text-3xl text-blue-500" />
                  </div>
                </div>
              </NGridItem>

              <!-- Sales volume -->
              <NGridItem>
                <div
                  class="h-24 flex items-center border-l-4 border-green-500 rounded-lg bg-green-50 p-4 dark:bg-green-900/20"
                >
                  <div class="w-full flex items-center justify-between">
                    <div class="min-w-0 flex-1">
                      <p class="truncate text-sm text-green-600 font-medium dark:text-green-400">
                        {{ $t('page.game.statistics.gameTotalSalesVolume') }}
                      </p>
                      <p class="mt-1 text-2xl text-green-900 font-bold dark:text-green-100">
                        {{ gameStatistics.totalSalesVolume.toLocaleString() }}
                      </p>
                    </div>
                    <SvgIcon icon="mdi:cart" class="ml-3 flex-shrink-0 text-3xl text-green-500" />
                  </div>
                </div>
              </NGridItem>

              <!-- Refund count -->
              <NGridItem>
                <div
                  class="h-24 flex items-center border-l-4 border-orange-500 rounded-lg bg-orange-50 p-4 dark:bg-orange-900/20"
                >
                  <div class="w-full flex items-center justify-between">
                    <div class="min-w-0 flex-1">
                      <p class="truncate text-sm text-orange-600 font-medium dark:text-orange-400">
                        {{ $t('page.game.statistics.gameRefundCount') }}
                      </p>
                      <p class="mt-1 text-2xl text-orange-900 font-bold dark:text-orange-100">
                        {{ gameStatistics.refundCount.toLocaleString() }}
                      </p>
                    </div>
                    <SvgIcon icon="mdi:undo" class="ml-3 flex-shrink-0 text-3xl text-orange-500" />
                  </div>
                </div>
              </NGridItem>

              <!-- Refund rate -->
              <NGridItem>
                <div
                  class="h-24 flex items-center border-l-4 border-red-500 rounded-lg bg-red-50 p-4 dark:bg-red-900/20"
                >
                  <div class="w-full flex items-center justify-between">
                    <div class="min-w-0 flex-1">
                      <p class="truncate text-sm text-red-600 font-medium dark:text-red-400">
                        {{ $t('page.game.statistics.gameRefundRate') }}
                      </p>
                      <p class="mt-1 text-2xl text-red-900 font-bold dark:text-red-100">
                        {{ gameStatistics.refundRate }}%
                      </p>
                    </div>
                    <SvgIcon icon="mdi:percent" class="ml-3 flex-shrink-0 text-3xl text-red-500" />
                  </div>
                </div>
              </NGridItem>

              <!-- Sales ranking -->
              <NGridItem>
                <div
                  class="h-24 flex items-center border-l-4 border-purple-500 rounded-lg bg-purple-50 p-4 dark:bg-purple-900/20"
                >
                  <div class="w-full flex items-center justify-between">
                    <div class="min-w-0 flex-1">
                      <p class="truncate text-sm text-purple-600 font-medium dark:text-purple-400">
                        {{ $t('page.game.statistics.salesRanking') }}
                      </p>
                      <p class="mt-1 text-2xl text-purple-900 font-bold dark:text-purple-100">
                        <NSpin v-if="isLoading" size="small" />
                        <span v-else-if="productRankingInfo.salesRank">
                          #{{ productRankingInfo.salesRank }}
                          <span class="text-sm text-purple-600 font-normal">
                            / {{ productRankingInfo.totalProducts }}
                          </span>
                        </span>
                        <span v-else class="text-sm text-purple-600">{{ $t('page.game.statistics.noRanking') }}</span>
                      </p>
                    </div>
                    <SvgIcon icon="mdi:trophy" class="ml-3 flex-shrink-0 text-3xl text-purple-500" />
                  </div>
                </div>
              </NGridItem>

              <!-- Volume ranking -->
              <NGridItem>
                <div
                  class="h-24 flex items-center border-l-4 border-indigo-500 rounded-lg bg-indigo-50 p-4 dark:bg-indigo-900/20"
                >
                  <div class="w-full flex items-center justify-between">
                    <div class="min-w-0 flex-1">
                      <p class="truncate text-sm text-indigo-600 font-medium dark:text-indigo-400">
                        {{ $t('page.game.statistics.quantityRanking') }}
                      </p>
                      <p class="mt-1 text-2xl text-indigo-900 font-bold dark:text-indigo-100">
                        <NSpin v-if="isLoading" size="small" />
                        <span v-else-if="productRankingInfo.quantityRank">
                          #{{ productRankingInfo.quantityRank }}
                          <span class="text-sm text-indigo-600 font-normal">
                            / {{ productRankingInfo.totalProducts }}
                          </span>
                        </span>
                        <span v-else class="text-sm text-indigo-600">{{ $t('page.game.statistics.noRanking') }}</span>
                      </p>
                    </div>
                    <SvgIcon icon="mdi:medal" class="ml-3 flex-shrink-0 text-3xl text-indigo-500" />
                  </div>
                </div>
              </NGridItem>
            </NGrid>
          </div>
        </NCard>

        <!-- Chart area -->
        <div class="space-y-6">
          <!-- Pie chart row -->
          <NGrid :cols="isMobile ? 1 : 2" :x-gap="16" :y-gap="16">
            <NGridItem>
              <NCard :bordered="false"  class="h-80">
                <div ref="regionVolumeChartRef" class="h-full w-full"></div>
              </NCard>
            </NGridItem>
            <NGridItem>
              <NCard :bordered="false" class="h-80">
                <div ref="currencyVolumeChartRef" class="h-full w-full"></div>
              </NCard>
            </NGridItem>
            <NGridItem>
              <NCard :bordered="false" class="h-80">
                <div ref="regionAmountChartRef" class="h-full w-full"></div>
              </NCard>
            </NGridItem>
            <NGridItem>
              <NCard :bordered="false" class="h-80">
                <div ref="currencyAmountChartRef" class="h-full w-full"></div>
              </NCard>
            </NGridItem>
          </NGrid>

          <!-- Bar chart and line chart -->
          <NGrid :cols="isMobile ? 1 : 2" :x-gap="16" :y-gap="16">
            <NGridItem>
              <NCard :bordered="false" class="h-96">
                <div ref="dailyChartRef" class="h-full w-full"></div>
              </NCard>
            </NGridItem>
            <NGridItem>
              <NCard :bordered="false" class="h-96">
                <div ref="hourlyChartRef" class="h-full w-full"></div>
              </NCard>
            </NGridItem>
          </NGrid>
        </div>
      </div>

      <div v-else class="h-64 flex items-center justify-center">
        <div class="text-center text-gray-500">
          <SvgIcon icon="mdi:chart-box-outline" class="mb-4 text-6xl" />
          <p>{{ $t('page.game.statistics.selectGame') }}</p>
        </div>
      </div>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped>
/* Custom styles */
</style>
