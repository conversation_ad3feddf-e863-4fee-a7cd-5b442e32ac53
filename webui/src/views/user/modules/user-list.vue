<script setup lang="tsx">
import { computed, h, nextTick, onMounted, reactive, ref, watch } from 'vue';
import type { DataTableColumns, DataTableRowKey } from 'naive-ui';
import { NAvatar, NButton, NSpace, NTag, useMessage, useDialog } from 'naive-ui';
import { useBoolean } from '@sa/hooks';
import { fetchUsersList, fetchSuspendUser, fetchActivateUser, fetchSendVerificationCode, fetchRemoveUserFromOrganization, fetchAssignableRoles } from '@/service/api';
import { handleApiError } from '@/utils/error-handler';
import { useAppStore } from '@/store/modules/app';
import { usePermission } from '@/hooks/common/permission';
import { $t } from '@/locales';
import Api from '@/service/api';
import SvgIcon from '@/components/custom/svg-icon.vue';
import ButtonIcon from '@/components/custom/button-icon.vue';
import OrganizationSelector from '@/components/common/organization-selector.vue';
import UserFormModal from './user-form-modal.vue';
import UserRoleModal from './user-role-modal.vue';

defineOptions({
  name: 'UserList'
});

// Use API returned user data structure
type UserData = Api.Organization.OrganizationUser;

const searchForm = reactive({
  name: '',
  email: '',
  status: '',
  organisation_id: undefined as number | undefined
});

const loading = ref(false);
const message = useMessage();
const dialog = useDialog();

const appStore = useAppStore();
const isMobile = computed(() => appStore.isMobile);

// Permission control
const permission = usePermission();

// Modal status
const { bool: modalVisible, setTrue: openModal } = useBoolean();
const { bool: roleModalVisible, setTrue: openRoleModal } = useBoolean();
const roleManagingUser = ref<UserData | null>(null);

// Batch selection related status
const checkedRowKeys = ref<DataTableRowKey[]>([]);

// User list data
const tableData = ref<UserData[]>([]);
const totalCount = ref(0);

// Permission control state
const isSystemAdmin = ref(false); // Whether current user is admin/root
const ownedOrganizationIds = ref<number[]>([]); // Organization IDs that current user owns (for owner users)

const columns = computed<DataTableColumns<UserData>>(() => [
  {
    type: 'selection',
    width: 50
  },
  {
    title: 'ID',
    key: 'id',
    width: 80
  },
  {
    title: $t('page.user.username'),
    key: 'name',
    width: 120,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: $t('page.user.email'),
    key: 'email',
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: $t('page.user.avatar'),
    key: 'avatar',
    width: 80,
    render: row => {
      return h(
        NAvatar,
        {
          size: 'small',
          fallbackSrc: '/default-avatar.png'
        },
        {
          default: () => row.name.charAt(0).toUpperCase()
        }
      );
    }
  },
  {
    title: $t('page.user.status'),
    key: 'status',
    width: 100,
    render: row => {
      // Determine user status based on email verification status and organization status
      const isVerified = row.email_verified_at !== null;
      const hasOrganizations = row.organisations && row.organisations.length > 0;

      let tagType: 'success' | 'error' | 'warning' = 'success';
      let statusText = '';

      if (!isVerified) {
        tagType = 'warning';
        statusText = $t('page.user.pending' as any);
      } else if (!hasOrganizations) {
        tagType = 'error';
        statusText = $t('page.user.inactive');
      } else {
        tagType = 'success';
        statusText = $t('page.user.active');
      }

      return h(
        NTag,
        {
          type: tagType,
          size: 'small'
        },
        {
          default: () => statusText
        }
      );
    }
  },

  {
    title: $t('page.user.createTime'),
    key: 'created_at',
    width: 120,
    render: row => {
      return new Date(row.created_at).toLocaleDateString();
    }
  },
  {
    title: $t('page.user.actions'),
    key: 'actions',
    width: 160,
    render: row => {
      const actions = [
        // h(ButtonIcon, {
        //   icon: 'mdi:pencil',
        //   tooltipContent: $t('common.edit'),
        //   class: 'text-primary',
        //   onClick: () => handleEditUser(row)
        // })
      ];

      // Add different operation buttons based on user status
      const userStatus = getUserStatus(row);
      if (userStatus === 'pending') {
        // Pending activation user: Send activation email button
        actions.push(
          h(ButtonIcon, {
            icon: 'mdi:email-send',
            tooltipContent: $t('page.user.sendActivationEmail' as any),
            class: 'text-warning',
            onClick: () => handleSendActivationEmail(row)
          })
        );
      } else {
        // Normal/Disabled user: Lock/Unlock button
        const isActive = userStatus === 'active';
        actions.push(
          h(ButtonIcon, {
            icon: isActive ? 'mdi:lock' : 'mdi:lock-open',
            tooltipContent: isActive ? $t('page.user.disable' as any) : $t('page.user.enable' as any),
            class: isActive ? 'text-warning' : 'text-success',
            onClick: () => handleToggleUserStatus(row)
          })
        );
      }

      // Manage roles button - requires manage roles permission
      if (permission.canManageUserRoles(row)) {
        actions.push(
          h(ButtonIcon, {
            icon: 'mdi:account-key',
            tooltipContent: $t('page.user.manageRoles' as any),
            class: 'text-purple',
            onClick: () => handleManageUserRoles(row)
          })
        );
      }

      // Display different operation buttons based on user roles
      if (permission.canDeleteUserPermanently(row)) {
        // System administrator: Display delete user button
        actions.push(
          h(ButtonIcon, {
            icon: 'mdi:delete',
            tooltipContent: $t('page.user.deleteUser'),
            class: 'text-error',
            onClick: () => handleDeleteUserPermanently(row)
          })
        );
      } else if (permission.canRemoveUserFromOrganization(row)) {
        // Organization owner: Display remove organization button
        actions.push(
          h(ButtonIcon, {
            icon: 'mdi:account-remove',
            tooltipContent: $t('page.user.removeFromOrganization'),
            class: 'text-warning',
            onClick: () => handleRemoveUserFromOrganization(row)
          })
        );
      }

      return h(
        NSpace,
        { size: 8 },
        {
          default: () => actions
        }
      );
    }
  }
]);

// Internationalization computed properties
const batchEnableText = computed(() => {
  try {
    return $t('page.user.batchEnable' as any);
  } catch {
    return 'Batch Enable';
  }
});

const batchDisableText = computed(() => {
  try {
    return $t('page.user.batchDisable' as any);
  } catch {
    return 'Batch Disable';
  }
});

const selectedCountText = computed(() => {
  try {
    return $t('page.user.selectedCount' as any);
  } catch {
    return 'Selected';
  }
});

const usersText = computed(() => {
  try {
    return $t('page.user.users' as any);
  } catch {
    return 'users';
  }
});

const selectAllText = computed(() => {
  try {
    return $t('page.user.selectAll' as any);
  } catch {
    return 'Select All';
  }
});

const clearSelectionText = computed(() => {
  try {
    return $t('page.user.clearSelection' as any);
  } catch {
    return 'Clear Selection';
  }
});

const batchEnableConfirmText = computed(() => {
  try {
    return $t('page.user.batchEnableConfirm' as any);
  } catch {
    return 'Confirm batch enable selected users?';
  }
});

const batchDisableConfirmText = computed(() => {
  try {
    return $t('page.user.batchDisableConfirm' as any);
  } catch {
    return 'Confirm batch disable selected users?';
  }
});

const batchOperationSuccessText = computed(() => {
  try {
    return $t('page.user.batchOperationSuccess' as any);
  } catch {
    return 'Batch operation completed';
  }
});

const noUsersSelectedText = computed(() => {
  try {
    return $t('page.user.noUsersSelected' as any);
  } catch {
    return 'Please select users to operate on';
  }
});

const confirmOperationText = computed(() => {
  try {
    return $t('page.user.confirmOperation' as any);
  } catch {
    return 'Confirm Operation';
  }
});

const confirmText = computed(() => {
  try {
    return $t('common.confirm' as any);
  } catch {
    return 'Confirm';
  }
});

const cancelText = computed(() => {
  try {
    return $t('common.cancel' as any);
  } catch {
    return 'Cancel';
  }
});

const noUsersToEnableText = computed(() => {
  try {
    return $t('page.user.noUsersToEnable' as any);
  } catch {
    return 'No users to enable in selection';
  }
});

const noUsersToDisableText = computed(() => {
  try {
    return $t('page.user.noUsersToDisable' as any);
  } catch {
    return 'No users to disable in selection';
  }
});

const pagination = reactive({
  page: 1,
  pageSize: 15,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 15, 20, 50],
  onChange: (page: number) => {
    pagination.page = page;
    loadUsers();
  },
  onUpdatePageSize: (pageSize: number) => {
    pagination.pageSize = pageSize;
    pagination.page = 1;
    loadUsers();
  }
});

// Check user permissions and initialize permission-based data
async function checkUserPermissions() {
  try {
    const { data } = await fetchAssignableRoles();

    if (data?.success) {
      const assignableData = data.data;

      // Check if user is admin/root (has system roles or empty details array)
      const hasSystemRoles = assignableData?.roles?.system && assignableData.roles.system.length > 0;
      const hasEmptyDetails = !assignableData?.details || assignableData.details.length === 0;

      isSystemAdmin.value = hasSystemRoles || hasEmptyDetails;

      // For non-admin users, extract owned organization IDs from details
      if (!isSystemAdmin.value && assignableData?.details) {
        const ownerRoles = assignableData.details.filter(role =>
          role.name === 'owner' && role.organisation_id
        );
        ownedOrganizationIds.value = ownerRoles.map(role => role.organisation_id!);
      }
    }
  } catch (error) {
    console.error('Failed to check user permissions:', error);
    // Default to non-admin if permission check fails
    isSystemAdmin.value = false;
    ownedOrganizationIds.value = [];
  }
}

// Load user list
async function loadUsers() {
  try {
    loading.value = true;
    const params: Api.Organization.UserSearchParams = {
      per_page: pagination.pageSize,
      only_owner: isSystemAdmin.value ? false : true
    };

    // Add search conditions
    if (searchForm.name) {
      params.name = searchForm.name;
    }
    if (searchForm.email) {
      params.email = searchForm.email;
    }

    // Handle organisation filtering
    let organisationIdsToFilter: number[] = [];

    // If specific organisation is selected in search form
    if (searchForm.organisation_id) {
      organisationIdsToFilter = [searchForm.organisation_id];
    }

    // Apply permission-based filtering for non-admin users
    if (!isSystemAdmin.value && ownedOrganizationIds.value.length > 0) {
      if (organisationIdsToFilter.length > 0) {
        // Filter selected organisation against owned organisations
        organisationIdsToFilter = organisationIdsToFilter.filter(id =>
          ownedOrganizationIds.value.includes(id)
        );
      } else {
        // No specific organisation selected, use all owned organisations
        organisationIdsToFilter = ownedOrganizationIds.value;
      }
    }

    // Set organisation_ids parameter if we have any IDs to filter
    if (organisationIdsToFilter.length > 0) {
      params.organisation_ids = organisationIdsToFilter.join(',');
    }

    const { data } = await fetchUsersList(params);
    if (data?.success) {
      tableData.value = data.data?.data || [];
      pagination.itemCount = data.data?.meta.total || 0;
      totalCount.value = data.data?.meta.total || 0;
    } else {
      handleApiError({ response: { data } }, message, $t('page.user.loadFailed' as any));
    }
  } catch (error) {
    handleApiError(error, message, $t('page.user.loadFailed' as any));
  } finally {
    loading.value = false;
  }
}

function handleSearch() {
  pagination.page = 1;
  loadUsers();
}

function handleReset() {
  Object.assign(searchForm, {
    name: '',
    email: '',
    status: '',
    organisation_id: undefined
  });
  pagination.page = 1;
  loadUsers();
}

function handleAddUser() {
  openModal();
}





function handleManageUserRoles(user: UserData) {
  roleManagingUser.value = user;
  openRoleModal();
}

async function handleToggleUserStatus(user: UserData) {
  const currentStatus = getUserStatus(user);
  const isDisabling = currentStatus === 'active';

  const confirmKey = isDisabling ? 'page.user.confirmDisableUser' : 'page.user.confirmEnableUser';
  const confirmMessage = $t(confirmKey as any, { username: user.name });

  dialog.warning({
    title: $t('common.tip'),
    content: confirmMessage,
    positiveText: $t('common.confirm'),
    negativeText: $t('common.cancel'),
    onPositiveClick: async () => {
      try {
        if (isDisabling) {
          await fetchSuspendUser(user.id);
        } else {
          await fetchActivateUser(user.id);
        }
        message.success($t('page.user.userStatusUpdated' as any));
        loadUsers(); // Reload data
      } catch (error) {
        handleApiError(error, message, $t('page.user.updateFailed' as any));
      }
    }
  });
}

async function handleSendActivationEmail(user: UserData) {
  const confirmMessage = $t('page.user.confirmSendActivationEmail' as any, { email: user.email });

  dialog.info({
    title: $t('common.tip'),
    content: confirmMessage,
    positiveText: $t('common.confirm'),
    negativeText: $t('common.cancel'),
    onPositiveClick: async () => {
      try {
        const { data } = await fetchSendVerificationCode(user.email);
        if (data.success) {
          message.success($t('page.user.activationEmailSent' as any, { email: user.email }));
        } else {
          handleApiError({ response: { data } }, message, $t('page.user.sendEmailFailed' as any));
        }
      } catch (error) {
        handleApiError(error, message, $t('page.user.sendEmailFailed' as any));
      }
    }
  });
}

async function handleDeleteUser(user: UserData) {
  const confirmMessage = $t('page.user.confirmSuspendUser' as any, { username: user.name });

  dialog.warning({
    title: $t('common.tip'),
    content: confirmMessage,
    positiveText: $t('common.confirm'),
    negativeText: $t('common.cancel'),
    onPositiveClick: async () => {
      try {
        await fetchSuspendUser(user.id);
        message.success($t('page.user.userSuspended' as any));
        loadUsers(); // Reload data
      } catch (error) {
        handleApiError(error, message, $t('page.user.suspendFailed' as any));
      }
    }
  });
}

// Permanently delete user (system admin only)
async function handleDeleteUserPermanently(user: UserData) {
  const confirmMessage = $t('page.user.confirmDeleteUser' as any, { username: user.name });

  dialog.error({
    title: $t('common.warning'),
    content: confirmMessage,
    positiveText: $t('common.confirm'),
    negativeText: $t('common.cancel'),
    onPositiveClick: async () => {
      try {
        // TODO: Implement delete user API call
        message.info($t('page.user.deleteUserNotImplemented' as any));
      } catch (error) {
        handleApiError(error, message, $t('page.user.deleteFailed' as any));
      }
    }
  });
}

// Remove user organization association (organization owner)
async function handleRemoveUserFromOrganization(user: UserData) {
  // Get organizations that current user can manage
  const managedOrgIds = permission.getManagedOrganizationIds();
  const userOrgIds = user.organisations?.map(org => org.id) || [];

  // Find removable organizations
  const removableOrgIds = userOrgIds.filter(orgId =>
    managedOrgIds.length === 0 || managedOrgIds.includes(orgId)
  );

  if (removableOrgIds.length === 0) {
    message.warning($t('page.user.noRemovableOrganizations' as any));
    return;
  }

  // If only one organization, remove directly
  if (removableOrgIds.length === 1) {
    const orgId = removableOrgIds[0];
    const org = user.organisations?.find(o => o.id === orgId);
    const confirmMessage = $t('page.user.confirmRemoveFromOrganization' as any, {
      username: user.name,
      organization: org?.name
    });

    dialog.warning({
      title: $t('common.tip'),
      content: confirmMessage,
      positiveText: $t('common.confirm'),
      negativeText: $t('common.cancel'),
      onPositiveClick: async () => {
        try {
          // Call the API to remove user organization association
          const { data } = await fetchRemoveUserFromOrganization(user.id, orgId);
          if (data.success) {
            message.success($t('page.user.removedFromOrganization' as any));
            loadUsers(); // Reload data
          } else {
            handleApiError({ response: { data } }, message, $t('page.user.removeFromOrganizationFailed' as any));
          }
        } catch (error) {
          handleApiError(error, message, $t('page.user.removeFromOrganizationFailed' as any));
        }
      }
    });
  } else {
    // Multiple organizations, display selection dialog
    // TODO: Implement multi-organization selection dialog
    message.info($t('page.user.selectOrganizationToRemove' as any));
  }
}

function handleUserSubmitted(_userData: UserData) {
  // Reload data after user submission
  loadUsers();
}



function handleUserRoleUpdated() {
  // Reload data after user role update
  loadUsers();
}

// Batch selection related functions
function handleRowCheck(rowKeys: DataTableRowKey[]) {
  checkedRowKeys.value = rowKeys;
}

function handleSelectAll() {
  const allKeys = tableData.value.map(item => item.id);
  checkedRowKeys.value = allKeys;
}

function handleClearSelection() {
  checkedRowKeys.value = [];
}

function handleBatchOperation(key: string) {
  if (checkedRowKeys.value.length === 0) {
    message.warning(noUsersSelectedText.value);
    return;
  }

  const selectedUsers = tableData.value.filter(user => checkedRowKeys.value.includes(user.id));

  if (key === 'enable') {
    handleBatchEnable(selectedUsers);
  } else if (key === 'disable') {
    handleBatchDisable(selectedUsers);
  }
}

function handleBatchEnable(users: UserData[]) {
  // Filter out users that can be enabled (non-active users)
  const usersToEnable = users.filter(user => getUserStatus(user) !== 'active');

  if (usersToEnable.length === 0) {
    message.warning(noUsersToEnableText.value);
    return;
  }

  dialog.warning({
    title: confirmOperationText.value,
    content: `${batchEnableConfirmText.value} (${usersToEnable.length} ${usersText.value})`,
    positiveText: confirmText.value,
    negativeText: cancelText.value,
    onPositiveClick: async () => {
      try {
        // Batch enable operation
        await Promise.all(usersToEnable.map(user => fetchActivateUser(user.id)));
        // Clear selection and reload data
        checkedRowKeys.value = [];
        loadUsers();
      } catch (error) {
        handleApiError(error, message, $t('page.user.batchOperationFailed' as any));
      }
    }
  });
}

function handleBatchDisable(users: UserData[]) {
  // 过滤出可以禁用的用户（活跃状态的用户）
  const usersToDisable = users.filter(user => getUserStatus(user) === 'active');

  if (usersToDisable.length === 0) {
    message.warning(noUsersToDisableText.value);
    return;
  }

  dialog.warning({
    title: confirmOperationText.value,
    content: `${batchDisableConfirmText.value} (${usersToDisable.length} ${usersText.value})`,
    positiveText: confirmText.value,
    negativeText: cancelText.value,
    onPositiveClick: async () => {
      try {
        // 批量禁用操作
        await Promise.all(usersToDisable.map(user => fetchSuspendUser(user.id)));
        // Clear selection and reload data
        checkedRowKeys.value = [];
        loadUsers();
      } catch (error) {
        handleApiError(error, message, $t('page.user.batchOperationFailed' as any));
      }
    }
  });
}

function handleRefresh() {
  loadUsers();
}

// 获取用户状态的辅助函数
function getUserStatus(user: UserData): 'active' | 'inactive' | 'pending' {
  const isVerified = user.email_verified_at !== null;
  const hasOrganizations = user.organisations && user.organisations.length > 0;

  if (!isVerified) {
    return 'pending';
  }
  if (!hasOrganizations) {
    return 'inactive';
  }
  return 'active';
}

// 组件挂载时先检查权限再加载数据
onMounted(async () => {
  await checkUserPermissions();
  loadUsers();
});
</script>

<template>
  <div class="space-y-3">
    <!-- Compact search area -->
    <div class="rounded-lg bg-gray-50 p-4 dark:bg-gray-800">
      <!-- Search fields and buttons -->
      <div :class="isMobile ? 'space-y-3' : 'flex flex-wrap gap-4 items-end'">
        <div class="min-w-40 flex flex-col flex-1 gap-1">
          <span class="text-sm text-gray-600 font-medium dark:text-gray-300">{{ $t('page.user.username') }}</span>
          <NInput
            v-model:value="searchForm.name"
            :placeholder="$t('page.user.usernamePlaceholder')"
            size="small"
            clearable
          >
            <template #prefix>
              <SvgIcon icon="mdi:account" class="text-gray-400" />
            </template>
          </NInput>
        </div>

        <div class="min-w-48 flex flex-col flex-1 gap-1">
          <span class="text-sm text-gray-600 font-medium dark:text-gray-300">{{ $t('page.user.email') }}</span>
          <NInput
            v-model:value="searchForm.email"
            :placeholder="$t('page.user.emailPlaceholder')"
            size="small"
            clearable
          >
            <template #prefix>
              <SvgIcon icon="mdi:email" class="text-gray-400" />
            </template>
          </NInput>
        </div>

        <div class="min-w-32 flex flex-col flex-1 gap-1">
          <span class="text-sm text-gray-600 font-medium dark:text-gray-300">{{ $t('page.user.status') }}</span>
          <NSelect
            v-model:value="searchForm.status"
            :placeholder="$t('page.user.statusPlaceholder')"
            :options="[
              { label: $t('page.user.active'), value: 'active' },
              { label: $t('page.user.inactive'), value: 'inactive' },
              { label: $t('page.user.pending' as any), value: 'pending' }
            ]"
            size="small"
            clearable
          />
        </div>

        <div class="min-w-40 flex flex-col flex-1 gap-1">
          <span class="text-sm text-gray-600 font-medium dark:text-gray-300">{{ $t('page.user.organization') }}</span>
          <OrganizationSelector
            v-model="searchForm.organisation_id"
            :placeholder="$t('page.user.organizationPlaceholder')"
            size="small"
            clearable
            show-all-option
            :all-option-value="undefined"
            :organization-ids="isSystemAdmin ? undefined : ownedOrganizationIds"
          />
        </div>

        <!-- Action buttons -->
        <div class="flex gap-2" :class="isMobile ? 'w-full justify-center' : 'flex-shrink-0'">
          <NButton size="small" @click="handleReset">
            {{ $t('common.reset') }}
          </NButton>
          <NButton type="primary" size="small" :loading="loading" @click="handleSearch">
            <template #icon>
              <SvgIcon icon="mdi:magnify" />
            </template>
            {{ $t('common.search') }}
          </NButton>
        </div>
      </div>
    </div>

    <!-- 操作栏 -->
    <div class="flex items-center justify-between">
      <div class="flex items-center gap-2">
        <!-- Add user button - requires create user permission -->
        <NButton v-if="permission.canCreateUser()" type="primary" @click="handleAddUser">
          <template #icon>
            <SvgIcon icon="mdi:plus" />
          </template>
          {{ $t('page.user.addUser') }}
        </NButton>
        <NButton :loading="loading" @click="handleRefresh">
          <template #icon>
            <SvgIcon icon="mdi:refresh" />
          </template>
          {{ $t('common.refresh') }}
        </NButton>
        <!-- Batch operation dropdown - requires batch operation permission -->
        <NDropdown
          v-if="permission.canBatchOperateUsers()"
          trigger="click"
          :options="[
            { label: batchEnableText, key: 'enable' },
            { label: batchDisableText, key: 'disable' }
          ]"
          @select="handleBatchOperation"
        >
          <NButton :disabled="checkedRowKeys.length === 0">
            {{ $t('page.user.batchOperations') }}
            <template #icon>
              <SvgIcon icon="mdi:chevron-down" />
            </template>
          </NButton>
        </NDropdown>
      </div>

      <!-- Selection status and batch operations -->
      <div v-if="checkedRowKeys.length > 0" class="flex items-center gap-2 text-sm text-gray-600">
        <span>{{ selectedCountText }} {{ checkedRowKeys.length }} {{ usersText }}</span>
        <NButton size="small" text @click="handleSelectAll">
          {{ selectAllText }}
        </NButton>
        <NButton size="small" text @click="handleClearSelection">
          {{ clearSelectionText }}
        </NButton>
      </div>
    </div>

    <!-- Data table -->
    <NDataTable
      v-model="checkedRowKeys"
      :columns="columns"
      :data="tableData"
      :loading="loading"
      :pagination="pagination"
      :bordered="false"
      size="small"
      flex-height
      class="h-[500px]"
      :row-key="row => row.id"
      @update:checked-row-keys="handleRowCheck"
    />

    <!-- User form modal -->
    <UserFormModal v-model="modalVisible" @submitted="handleUserSubmitted" />



    <!-- User role management modal -->
    <UserRoleModal v-model="roleModalVisible" :user="roleManagingUser" @updated="handleUserRoleUpdated" />
  </div>
</template>

<style scoped></style>
