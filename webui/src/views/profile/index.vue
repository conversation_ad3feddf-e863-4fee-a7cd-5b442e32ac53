<script setup lang="ts">
import { ref, computed } from 'vue';
import type { FormInst } from 'naive-ui';
import { useAuthStore } from '@/store/modules/auth';
import { $t } from '@/locales';
import SvgIcon from '@/components/custom/svg-icon.vue';
import AvatarUpload from '@/components/custom/avatar-upload.vue';
import PasswordChange from '@/components/custom/password-change.vue';
// fetchUpdateProfile removed since profile fields are readonly

defineOptions({
  name: 'Profile'
});

const authStore = useAuthStore();

// Form reference (removed since no longer needed for readonly fields)

// User info
const userInfo = computed(() => authStore.userInfo);
const userEmail = computed(() => authStore.userEmail);
const completeUserInfo = computed(() => authStore.completeUserInfo);

// Form data
const profileForm = ref({
  name: userInfo.value.userName || '',
  email: userEmail.value || ''
});

// Avatar URL
const avatarUrl = ref(completeUserInfo.value?.avatar_url || '');

// Loading states (removed since no profile updates)

// Form validation rules (removed since fields are readonly)

// Methods (profile update removed since fields are readonly)

const handleAvatarSuccess = (url: string) => {
  avatarUrl.value = url;
  // Refresh user info to get updated avatar
  authStore.initUserInfo();
};

const handleAvatarError = (error: string) => {
  console.error('Avatar upload error:', error);
};

const handlePasswordSuccess = () => {
  console.log('Password changed successfully');
};

const handlePasswordError = (error: string) => {
  console.error('Password change error:', error);
};
</script>

<template>
  <div class="profile-page">
    <NSpace vertical :size="24">
      <!-- Page Header -->
      <NCard :bordered="false" class="header-card">
        <template #header>
          <div class="flex items-center gap-4">
            <!-- User Avatar -->
            <div class="avatar-section">
              <NAvatar
                :size="64"
                :src="avatarUrl || completeUserInfo?.avatar_url"
                round
                fallback-src="/default-avatar.png"
              >
                <SvgIcon icon="ph:user-circle" class="text-32px" />
              </NAvatar>
            </div>

            <!-- User Info -->
            <div class="flex-1">
              <h1 class="text-24px font-bold">{{ $t('page.user.profile.title') }}</h1>
              <p class="text-14px text-gray-500 mt-1">
                {{ $t('page.user.profile.basicInfo') }}
              </p>
              <div class="flex items-center gap-2 text-14px text-gray-600 mt-2">
                <span class="font-medium">{{ profileForm.name }}</span>
                <span>•</span>
                <span>{{ profileForm.email }}</span>
              </div>
            </div>
          </div>
        </template>
      </NCard>

      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Profile Form -->
        <div class="lg:col-span-2">
          <NCard :bordered="false" class="profile-card">
            <template #header>
              <div class="flex items-center gap-2">
                <SvgIcon icon="ph:user" class="text-16px" />
                <span class="font-medium">{{ $t('page.user.profile.basicInfo') }}</span>
              </div>
            </template>

            <NForm
              :model="profileForm"
              label-placement="left"
              label-width="120px"
            >
              <NFormItem :label="$t('page.user.username')">
                <NInput
                  v-model:value="profileForm.name"
                  readonly
                  :placeholder="$t('page.user.usernamePlaceholder')"
                />
              </NFormItem>

              <NFormItem :label="$t('page.user.email')">
                <NInput
                  v-model:value="profileForm.email"
                  readonly
                  :placeholder="$t('page.user.emailPlaceholder')"
                />
              </NFormItem>

              <NFormItem>
                <NText depth="3" class="text-sm">
                  {{ $t('page.user.profile.readonlyFieldsNote') }}
                </NText>
              </NFormItem>
            </NForm>
          </NCard>

          <!-- Password Change Form -->
          <NCard :bordered="false" class="password-card mt-6">
            <template #header>
              <div class="flex items-center gap-2">
                <SvgIcon icon="ph:lock" class="text-16px" />
                <span class="font-medium">{{ $t('page.user.profile.changePassword') }}</span>
              </div>
            </template>

            <PasswordChange
              @success="handlePasswordSuccess"
              @error="handlePasswordError"
            />
          </NCard>
        </div>

        <!-- Avatar Upload Sidebar -->
        <div class="space-y-4">
          <NCard :bordered="false" class="avatar-card">
            <template #header>
              <div class="flex items-center gap-2">
                <SvgIcon icon="ph:image" class="text-16px" />
                <span class="font-medium">{{ $t('page.user.profile.avatarUpload') }}</span>
              </div>
            </template>

            <div class="text-center">
              <!-- Avatar Upload Component with integrated preview -->
              <AvatarUpload
                :avatar-url="avatarUrl || completeUserInfo?.avatar_url || ''"
                :size="120"
                @success="handleAvatarSuccess"
                @error="handleAvatarError"
              />
            </div>
          </NCard>
        </div>
      </div>
    </NSpace>
  </div>
</template>

<style scoped>
.profile-page {
  padding: 16px;
  min-height: 100vh;
  background: #f5f5f5;
}

.header-card,
.profile-card,
.password-card,
.avatar-card {
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  background: white;
}

.avatar-preview {
  display: flex;
  justify-content: center;
  align-items: center;
}

.avatar-section {
  flex-shrink: 0;
}

.avatar-section .n-avatar {
  border: 3px solid #f0f0f0;
  transition: all 0.3s ease;
}

.avatar-section .n-avatar:hover {
  border-color: #18a058;
  transform: scale(1.05);
}

.avatar-card .n-avatar {
  border: 2px solid #f0f0f0;
  transition: all 0.3s ease;
}

.avatar-card .n-avatar:hover {
  border-color: #18a058;
}
</style>
