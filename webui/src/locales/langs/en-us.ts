const local: App.I18n.Schema = {
  system: {
    title: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    updateTitle: 'System Version Update Notification',
    updateContent: 'A new version of the system has been detected. Do you want to refresh the page immediately?',
    updateConfirm: 'Refresh immediately',
    updateCancel: 'Later'
  },
  common: {
    action: 'Action',
    add: 'Add',
    addSuccess: 'Add Success',
    backToHome: 'Back to home',
    batchDelete: 'Batch Delete',
    cancel: 'Cancel',
    close: 'Close',
    check: 'Check',
    expandColumn: 'Expand Column',
    columnSetting: 'Column Setting',
    config: 'Config',
    confirm: 'Confirm',
    delete: 'Delete',
    deleteSuccess: 'Delete Success',
    confirmDelete: 'Are you sure you want to delete?',
    edit: 'Edit',
    warning: 'Warning',
    error: 'Error',
    index: 'Index',
    keywordSearch: 'Please enter keyword',
    logout: 'Logout',
    logoutConfirm: 'Are you sure you want to log out?',
    lookForward: 'Coming soon',
    modify: 'Modify',
    modifySuccess: 'Modify Success',
    noData: 'No Data',
    notAvailable: 'N/A',
    operate: 'Operate',
    pleaseCheckValue: 'Please check whether the value is valid',
    refresh: 'Refresh',
    reset: 'Reset',
    save: 'Save',
    search: 'Search',
    status: 'Status',
    switch: 'Switch',
    tip: 'Tip',
    trigger: 'Trigger',
    update: 'Update',
    updateSuccess: 'Update Success',
    view: 'View',
    userCenter: 'User Center',
    profile: 'Profile',
    selectAll: 'Select All',
    deselectAll: 'Deselect All',
    selectOrganization: 'Please select organization',
    selectMultipleOrganizations: 'Please select organizations',
    organization: 'Organization',
    allOrganizations: 'All Organizations',
    searchOrganizations: 'Search organizations',
    noOrganizations: 'No organizations',
    noSearchResults: 'No search results',
    unit: {
      reports: 'reports',
      orders: 'orders',
      organizations: 'orgs',
      people: 'people',
      games: 'games',
      copies: 'copies'
    },
    yesOrNo: {
      yes: 'Yes',
      no: 'No'
    },
    verified: 'Verified',
    unverified: 'Unverified',
    pending: 'Pending',
    loading: 'Loading...',
    loadError: 'Load Error',
    retry: 'Retry',
    uploading: 'Uploading...',
    weak: 'Weak',
    medium: 'Medium',
    strong: 'Strong',
    passwordRequirements: 'Password Requirements',
    passwordMinLength: 'At least 6 characters',
    passwordComplexity: 'Recommend including uppercase, lowercase, numbers and special characters',
    passwordRecommendation: 'Use a strong password to protect your account'
  },
  request: {
    logout: 'Logout user after request failed',
    logoutMsg: 'User status is invalid, please log in again',
    logoutWithModal: 'Pop up modal after request failed and then log out user',
    logoutWithModalMsg: 'User status is invalid, please log in again',
    refreshToken: 'The requested token has expired, refresh the token',
    tokenExpired: 'The requested token has expired'
  },
  theme: {
    themeSchema: {
      title: 'Theme Schema',
      light: 'Light',
      dark: 'Dark',
      auto: 'Follow System'
    },
    grayscale: 'Grayscale',
    colourWeakness: 'Colour Weakness',
    layoutMode: {
      title: 'Layout Mode',
      vertical: 'Vertical Menu Mode',
      horizontal: 'Horizontal Menu Mode',
      'vertical-mix': 'Vertical Mix Menu Mode',
      'horizontal-mix': 'Horizontal Mix menu Mode',
      reverseHorizontalMix: 'Reverse first level menus and child level menus position'
    },
    recommendColor: 'Apply Recommended Color Algorithm',
    recommendColorDesc: 'The recommended color algorithm refers to',
    themeColor: {
      title: 'Theme Color',
      primary: 'Primary',
      info: 'Info',
      success: 'Success',
      warning: 'Warning',
      error: 'Error',
      followPrimary: 'Follow Primary'
    },
    scrollMode: {
      title: 'Scroll Mode',
      wrapper: 'Wrapper',
      content: 'Content'
    },
    page: {
      animate: 'Page Animate',
      mode: {
        title: 'Page Animate Mode',
        fade: 'Fade',
        'fade-slide': 'Slide',
        'fade-bottom': 'Fade Zoom',
        'fade-scale': 'Fade Scale',
        'zoom-fade': 'Zoom Fade',
        'zoom-out': 'Zoom Out',
        none: 'None'
      }
    },
    fixedHeaderAndTab: 'Fixed Header And Tab',
    header: {
      height: 'Header Height',
      breadcrumb: {
        visible: 'Breadcrumb Visible',
        showIcon: 'Breadcrumb Icon Visible'
      },
      multilingual: {
        visible: 'Display multilingual button'
      },
      globalSearch: {
        visible: 'Display GlobalSearch button'
      }
    },
    tab: {
      visible: 'Tab Visible',
      cache: 'Tag Bar Info Cache',
      height: 'Tab Height',
      mode: {
        title: 'Tab Mode',
        chrome: 'Chrome',
        button: 'Button'
      }
    },
    sider: {
      inverted: 'Dark Sider',
      width: 'Sider Width',
      collapsedWidth: 'Sider Collapsed Width',
      mixWidth: 'Mix Sider Width',
      mixCollapsedWidth: 'Mix Sider Collapse Width',
      mixChildMenuWidth: 'Mix Child Menu Width'
    },
    footer: {
      visible: 'Footer Visible',
      fixed: 'Fixed Footer',
      height: 'Footer Height',
      right: 'Right Footer'
    },
    watermark: {
      visible: 'Watermark Full Screen Visible',
      text: 'Watermark Text'
    },
    themeDrawerTitle: 'Theme Configuration',
    pageFunTitle: 'Page Function',
    resetCacheStrategy: {
      title: 'Reset Cache Strategy',
      close: 'Close Page',
      refresh: 'Refresh Page'
    },
    configOperation: {
      copyConfig: 'Copy Config',
      copySuccessMsg: 'Copy Success, Please replace the variable "themeSettings" in "src/theme/settings.ts"',
      resetConfig: 'Reset Config',
      resetSuccessMsg: 'Reset Success'
    }
  },
  route: {
    login: 'Login',
    403: 'No Permission',
    404: 'Page Not Found',
    500: 'Server Error',
    'iframe-page': 'Iframe',
    home: 'Home',
    organization: 'Organizations',
    user: 'Users',
    profile: 'Profile',
    game: 'Games',
    sales: 'Sales Statistics',
    finance: 'Financial Reports',
    admin: 'Admin Functions',
    admin_horizon: 'Horizon Monitor',
    admin_settings: 'Admin Settings',
    'admin_import-logs': 'Import Logs',
    'admin_system-logs': 'System Logs',
    join: 'Join Organization'
  },
  invitation: {
    title: 'Organization Invitation',
    loading: 'Loading invitation information...',
    invalid: 'Invalid Invitation',
    expired: 'Invitation Expired',
    usageLimitReached: 'Usage Limit Reached',
    invalidLink: 'This invitation link is invalid.',
    expiredLink: 'This invitation link has expired and cannot be used.',
    usageLimitLink: 'This invitation link has reached the maximum usage limit.',
    invitedToJoin: 'You are invited to join the following organization',
    organizationInfo: 'Organization Information',
    inviterRole: 'Inviter Role',
    inviter: 'Inviter',
    expirationTime: 'Expiration Time',
    restrictedEmail: 'Restricted Email',
    cannotAccept: 'Cannot Accept Invitation',
    acceptInvitation: 'Accept Invitation',
    loginToAccept: 'Login to Accept Invitation',
    registerToAccept: 'Register to Accept Invitation',
    backToHome: 'Back to Home',
    joinSuccess: 'Successfully joined the organization!',
    joinFailed: 'Failed to accept invitation',
    emailRestriction: 'This invitation is restricted to {email}',
    getVerificationCode: 'Get Verification Code',
    resendCode: 'Resend ({time}s)',
    verificationCodeSent: 'Verification code has been sent to your email',
    sendCodeFailed: 'Failed to send verification code',
    enterEmail: 'Please enter email address',
    enterValidEmail: 'Please enter a valid email address',
    enterVerificationCode: 'Please enter verification code',
    enterPassword: 'Please enter password',
    enterPasswordAgain: 'Please enter password again',
    emailPlaceholder: 'Please enter email address',
    codePlaceholder: 'Please enter verification code',
    passwordPlaceholder: 'Please enter password',
    confirmPasswordPlaceholder: 'Please enter password again',
    register: 'Register',
    codeLogin: 'Code Login',
    roles: {
      owner: 'Owner',
      member: 'Member'
    }
  },
  page: {
    login: {
      common: {
        loginOrRegister: 'Login / Register',
        userNamePlaceholder: 'Please enter username or email',
        phonePlaceholder: 'Please enter phone number',
        codePlaceholder: 'Please enter verification code',
        passwordPlaceholder: 'Please enter password',
        confirmPasswordPlaceholder: 'Please enter password again',
        codeLogin: 'Verification code login',
        confirm: 'Confirm',
        back: 'Back',
        validateSuccess: 'Verification passed',
        loginSuccess: 'Login successfully',
        welcomeBack: 'Welcome back, {userName} !'
      },
      pwdLogin: {
        title: 'Password Login',
        rememberMe: 'Remember me',
        forgetPassword: 'Forget password?',
        register: 'Register',
        otherAccountLogin: 'Other Account Login',
        otherLoginMode: 'Other Login Mode',
        superAdmin: 'Super Admin',
        admin: 'Admin',
        user: 'User'
      },
      codeLogin: {
        title: 'Verification Code Login',
        getCode: 'Get verification code',
        reGetCode: 'Reacquire after {time}s',
        sendCodeSuccess: 'Verification code sent successfully',
        imageCodePlaceholder: 'Please enter image verification code'
      },
      register: {
        title: 'Register',
        agreement: 'I have read and agree to',
        protocol: '《User Agreement》',
        policy: '《Privacy Policy》'
      },
      resetPwd: {
        title: 'Reset Password'
      },
      bindWeChat: {
        title: 'Bind WeChat'
      }
    },
    home: {
      branchDesc:
        'For the convenience of everyone in developing and updating the merge, we have streamlined the code of the main branch, only retaining the homepage menu, and the rest of the content has been moved to the example branch for maintenance. The preview address displays the content of the example branch.',
      greeting: {
        morning: 'Good morning, {userName}!',
        afternoon: 'Good afternoon, {userName}!',
        evening: 'Good evening, {userName}!'
      },
      timeControl: {
        quickSelect: 'Quick Select',
        pastYear: 'Past Year',
        pastQuarter: 'Past Quarter',
        pastMonth: 'Past Month',
        pastWeek: 'Past Week',
        customRange: 'Custom Range',
        startDate: 'Start Date',
        endDate: 'End Date',
        previousPeriod: 'Previous Period',
        nextPeriod: 'Next Period',
        selectDateRange: 'Select Date Range',
        organization: 'Organization',
        shortLabels: {
          week: 'W',
          month: 'M',
          quarter: 'Q',
          year: 'Y',
          custom: 'Custom'
        },
        resetToCurrent: 'Reset'
      },
      weatherDesc: 'Today is cloudy to clear, 20℃ - 25℃!',
      projectCount: 'Project Count',
      todo: 'Todo',
      message: 'Message',
      downloadCount: 'Download Count',
      registerCount: 'Register Count',
      schedule: 'Work and rest Schedule',
      study: 'Study',
      work: 'Work',
      rest: 'Rest',
      entertainment: 'Entertainment',
      userCount: 'User Count',
      gameCount: 'Game Count',
      orderCount: 'Order Count',
      reportCount: 'Report Count',
      totalSalesAmount: 'Total Sales Amount',
      dailyOrderSales: 'Daily Order Sales Statistics',
      dailySalesAmount: 'Daily Sales Amount Statistics',
      dailySalesQuantity: 'Daily Sales Quantity Statistics',
      salesAmountByRegion: 'Sales Amount by Region',
      salesQuantityByRegion: 'Sales Quantity by Region',
      salesByRegion: 'Sales Summary by Region',
      orderAmount: 'Order Amount',
      orderQuantity: 'Order Quantity',
      salesAmount: 'Sales Amount',
      salesQuantity: 'Sales Quantity',
      topSalesAmountRanking: 'Top 10 Daily Sales Amount',
      topSalesQuantityRanking: 'Top 10 Daily Sales Quantity',
      gameName: 'Game Name',
      ranking: 'Ranking',
      units: 'units',
      viewGameStats: 'View Game Statistics',
      businessStats: 'Business Statistics Overview',
      totalRevenue: 'Total Revenue',
      monthlyGrowth: 'Monthly Growth',
      activeUsers: 'Active Users',
      systemMessages: 'System Messages',
      notifications: 'Notifications',
      viewAll: 'View All',
      markAsRead: 'Mark as Read',
      newOrder: 'New Order',
      systemUpdate: 'System Update',
      securityAlert: 'Security Alert',
      maintenanceNotice: 'Maintenance Notice',
      minutes: 'minutes ago',
      hours: 'hours ago',
      days: 'days ago',
      dealCount: 'Deal Count',
      projectNews: {
        title: 'Project News',
        moreNews: 'More News',
        desc1: 'Soybean created the open source project soybean-admin on May 28, 2021!',
        desc2: 'Yanbowe submitted a bug to soybean-admin, the multi-tab bar will not adapt.',
        desc3: 'Soybean is ready to do sufficient preparation for the release of soybean-admin!',
        desc4: 'Soybean is busy writing project documentation for soybean-admin!',
        desc5: 'Soybean just wrote some of the workbench pages casually, and it was enough to see!'
      },
      creativity: 'Creativity'
    },
    organization: {
      title: 'Organization Management',
      list: 'Organization List',
      stats: 'Organization Statistics',
      totalOrganizations: 'Total Organizations',
      activeOrganizations: 'Active Organizations',
      pendingOrganizations: 'Pending Organizations',
      totalUsers: 'Total Users',
      newOrganizations: 'New This Month',
      searchPlaceholder: 'Search by organization name',
      addOrganization: 'Add Organization',
      editOrganization: 'Edit Organization',
      organizationName: 'Organization Name',
      organizationNamePlaceholder: 'Please enter organization name',
      organizationNameRequired: 'Please enter organization name',
      organizationCode: 'Organization Code',
      organizationCodePlaceholder: 'Please enter organization code (optional)',
      organizationCodeRequired: 'Please enter organization code',
      description: 'Description',
      descriptionPlaceholder: 'Please enter description (optional)',
      remarks: 'Remarks',
      remarksPlaceholder: 'Please enter remarks (optional)',
      allStatus: 'All Status',
      statusPlaceholder: 'Please select status',
      statusRequired: 'Please select status',
      createTime: 'Create Time',
      userCount: 'User Count',
      status: 'Status',
      actions: 'Actions',
      edit: 'Edit',
      viewDetails: 'View Details',
      manageUsers: 'Manage Users',
      inviteUser: 'Invite User',
      pending: 'Pending',
      active: 'Active',
      suspended: 'Suspended',
      suspend: 'Suspend',
      removeUser: 'Remove User',
      confirmSuspend: 'Confirm Suspend',
      suspendConfirmTitle: 'Confirm Suspend',
      suspendConfirmContent: 'Are you sure you want to suspend organization "{name}"? After suspension, this organization will not be able to use system functions normally.',
      confirmSuspendButton: 'Confirm Suspend',
      addSuccess: 'Organization added successfully',
      editSuccess: 'Organization updated successfully',
      addFailed: 'Failed to add organization',
      editFailed: 'Failed to update organization',
      loadFailed: 'Failed to load organization list',
      fetchDetailFailed: 'Failed to fetch organization details',
      suspendSuccess: 'Organization suspended successfully',
      suspendFailed: 'Failed to suspend organization',
      userManagement: {
        title: 'Organization User Management',
        currentOrganization: 'Current Organization',
        userList: 'Member List',
        confirmRemoveTitle: 'Confirm Remove',
        removeUserConfirmContent: 'Are you sure you want to remove user "{userName}" from organization "{organizationName}"?',
        confirmRemoveButton: 'Confirm Remove',
        loadUsersFailed: 'Failed to load user list',
        removeUserSuccess: 'User removed successfully',
        removeUserFailed: 'Failed to remove user'
      },
      invite: {
        title: 'Invite User to Organization',
        inviteToOrganization: 'Invite to Organization',
        inviteeEmail: 'Invitee Email',
        emailPlaceholder: "Please enter the invitee's email address",
        inviteeRole: 'Invitee Role',
        rolePlaceholder: 'Please select a role',
        roleRequired: 'Please select a role',
        inviteLink: 'Invitation Link',
        generate: 'Generate Invitation Link',
        regenerate: 'Regenerate Link',
        copyLink: 'Copy Link',
        sendEmail: 'Send Email',
        linkGenerated: 'Invitation link generated successfully',
        copySuccess: 'Link copied to clipboard',
        emailSentSuccess: 'Invitation email sent successfully',
        emailSentFailed: 'Failed to send invitation email',
        linkExpireHint: 'The invitation link will expire in 7 days',
        generateSuccess: 'Invitation link generated successfully',
        generateFailed: 'Failed to generate invitation link'
      }
    },
    user: {
      title: 'User Management',
      list: 'User List',
      stats: 'User Statistics',
      totalUsers: 'Total Users',
      activeUsers: 'Active Users',
      pendingActivation: 'Pending Activation',
      newUsers: 'New This Month',
      searchConditions: 'Search Conditions',
      username: 'Username',
      email: 'Email',
      status: 'Status',
      organization: 'Organization',
      avatar: 'Avatar',
      role: 'Role',
      createTime: 'Create Time',
      actions: 'Actions',
      addUser: 'Add User',
      batchOperations: 'Batch Operations',
      batchEnable: 'Batch Enable',
      batchDisable: 'Batch Disable',
      selectedCount: 'Selected',
      users: 'users',
      selectAll: 'Select All',
      clearSelection: 'Clear Selection',
      batchEnableConfirm: 'Confirm batch enable for selected users?',
      batchDisableConfirm: 'Confirm batch disable for selected users?',
      batchOperationSuccess: 'Batch operation completed',
      noUsersSelected: 'Please select users to operate first',
      confirmOperation: 'Confirm Operation',
      noUsersToEnable: 'No users to enable in the selected users',
      noUsersToDisable: 'No users to disable in the selected users',
      usernamePlaceholder: 'Enter username',
      emailPlaceholder: 'Enter email',
      statusPlaceholder: 'Select status',
      organizationPlaceholder: 'Enter organization name',
      active: 'Active',
      inactive: 'Inactive',
      pending: 'Pending',
      disable: 'Disable',
      enable: 'Enable',
      sendActivationEmail: 'Send Activation Email',
      activationEmailSent: 'Activation Email Sent',
      confirmDisableUser: 'Are you sure to disable user {username}?',
      confirmEnableUser: 'Are you sure to enable user {username}?',
      confirmSendActivationEmail: 'Are you sure to send activation email to {email}?',
      userStatusUpdated: 'User status updated',
      admin: 'Admin',
      normalUser: 'Normal User',
      owner: 'Owner',
      member: 'Member',
      name: 'Name',
      basicInfo: 'Basic Information',
      userId: 'User ID',
      createdAt: 'Created At',
      updatedAt: 'Updated At',
      roles: 'Roles',
      systemRoles: 'System Roles',
      organisationRoles: 'Organisation Roles',
      organisations: 'Organisations',
      noUserInfo: 'No user information available',
      userForm: {
        title: 'User Information',
        addTitle: 'Add User',
        editTitle: 'Edit User',
        username: 'Username',
        email: 'Email',
        password: 'Password',
        confirmPassword: 'Confirm Password',
        role: 'Role',
        organization: 'Organization',
        status: 'Status',
        avatar: 'Avatar',
        usernamePlaceholder: 'Enter username',
        emailPlaceholder: 'Enter email address',
        passwordPlaceholder: 'Enter password',
        confirmPasswordPlaceholder: 'Enter password again',
        rolePlaceholder: 'Select role',
        organizationPlaceholder: 'Enter organization',
        statusPlaceholder: 'Select status',
        submit: 'Submit',
        cancel: 'Cancel',
        required: 'This field is required',
        usernameRequired: 'Please enter username',
        emailRequired: 'Please enter email address',
        passwordRequired: 'Please enter password',
        confirmPasswordRequired: 'Please enter password again',
        roleRequired: 'Please select role',
        organizationRequired: 'Please enter organization',
        statusRequired: 'Please select status',
        emailInvalid: 'Invalid email format',
        passwordInvalid: 'Password must be at least 6 characters',
        confirmPasswordInvalid: 'Passwords do not match',
        usernameInvalid: 'Username must be between 2-50 characters',
        uploadAvatar: 'Upload Avatar',
        changeAvatar: 'Change Avatar',
        removeAvatar: 'Remove Avatar',
        avatarHint: 'JPG, PNG, GIF, max 2MB',
        avatarTypeError: 'Invalid avatar format, please upload JPG, PNG, GIF format images',
        avatarSizeError: 'Avatar file size cannot exceed 2MB',
        avatarUploadSuccess: 'Avatar uploaded successfully',
        avatarUploadError: 'Avatar upload failed',
        avatarRemoveSuccess: 'Avatar removed successfully',
        addOrganization: 'Add Organization',
        organizationAddSuccess: 'Organization added successfully',
        noOrganization: 'No organization assigned',
        addSuccess: 'User added successfully',
        editSuccess: 'User updated successfully',
        addFailed: 'Failed to add user',
        editFailed: 'Failed to update user'
      },
      // User role management related translations
      manageUserRoles: 'Manage User Roles - {name}',
      assignNewRole: 'Assign New Role',
      selectRole: 'Select Role',
      selectOrganization: 'Select Organization',
      assignRole: 'Assign Role',
      currentRoles: 'Current Roles',
      transferOwnership: 'Transfer Ownership',
      noRolesAssigned: 'No roles assigned',
      loadUserRolesFailed: 'Failed to load user roles',
      // New role management translations
      systemPermissions: 'System Permissions',
      organizationPermissions: 'Organization Permissions',
      currentSystemRoles: 'Current System Roles',
      currentOrgRoles: 'Current Organization Roles',
      assignSystemRole: 'Assign System Role',
      assignOrgRole: 'Assign Organization Role',
      selectSystemRole: 'Select System Role',
      selectOrgRole: 'Select Organization Role',
      noSystemPermissions: 'No system permissions',
      noOrgRolesAssigned: 'No organization roles assigned',
      deleteUser: 'Delete User',
      removeFromOrganization: 'Remove from Organization',
      confirmDeleteUser: 'Are you sure you want to delete user {username}? This action cannot be undone!',
      confirmRemoveFromOrganization: 'Are you sure you want to remove user {username} from organization {organization}?',
      userDeleted: 'User deleted successfully',
      removedFromOrganization: 'User removed from organization successfully',
      deleteFailed: 'Failed to delete user',
      removeFromOrganizationFailed: 'Failed to remove user from organization',
      deleteUserNotImplemented: 'Delete user feature is not yet implemented',
      noRemovableOrganizations: 'No removable organizations',
      selectOrganizationToRemove: 'Please select organization to remove',
      assignRoleSuccess: 'Role assigned successfully',
      assignRoleFailed: 'Failed to assign role',
      confirmRemoveRole: 'Are you sure you want to remove the {role} role from user {user}?',
      removeRoleSuccess: 'Role removed successfully',
      removeRoleFailed: 'Failed to remove role',
      confirmTransferOwner: 'Are you sure you want to transfer ownership to user {user}?',
      transferOwnerSuccess: 'Ownership transferred successfully',
      transferOwnerFailed: 'Failed to transfer ownership',
      // User organization management related translations
      manageUserOrganizations: 'Manage User Organizations - {name}',
      loadUserOrganizationsFailed: 'Failed to load user organizations',
      syncOrganizationsSuccess: 'Organization associations synced successfully',
      syncOrganizationsFailed: 'Failed to sync organization associations',
      selectUserOrganizations: 'Select user organizations',
      selectOrganizationsPlaceholder: 'Please select organizations',
      currentOrganizations: 'Current Organizations',
      // User list action buttons
      manageOrganizations: 'Manage Organizations',
      manageRoles: 'Manage Roles',
      // User operation related translations
      loadFailed: 'Failed to load user list',
      loadUserDetailFailed: 'Failed to load user details',
      updateFailed: 'Failed to update user status',
      sendEmailFailed: 'Failed to send email',
      confirmSuspendUser: 'Are you sure to suspend user {username}?',
      userSuspended: 'User suspended successfully',
      suspendFailed: 'Failed to suspend user',
      batchOperationFailed: 'Batch operation failed',
      // Profile related translations
      profile: {
        title: 'Profile',
        basicInfo: 'Basic Information',
        changePassword: 'Change Password',
        currentPassword: 'Current Password',
        newPassword: 'New Password',
        confirmNewPassword: 'Confirm New Password',
        currentPasswordPlaceholder: 'Enter current password',
        newPasswordPlaceholder: 'Enter new password',
        confirmNewPasswordPlaceholder: 'Enter new password again',
        changePasswordSuccess: 'Password changed successfully',
        changePasswordFailed: 'Failed to change password',
        currentPasswordRequired: 'Please enter current password',
        newPasswordRequired: 'Please enter new password',
        confirmNewPasswordRequired: 'Please confirm new password',
        passwordMismatch: 'Passwords do not match',
        currentPasswordIncorrect: 'Current password is incorrect',
        avatarUpload: 'Avatar Upload',
        changeAvatar: 'Change Avatar',
        uploadAvatar: 'Upload Avatar',
        currentAvatar: 'Current Avatar',
        avatarHint: 'JPG, PNG, GIF, max 2MB',
        uploadSuccess: 'Upload successful',
        uploadFailed: 'Upload failed',
        updateProfileSuccess: 'Profile updated successfully',
        updateProfileFailed: 'Failed to update profile',
        readonlyFieldsNote: 'Username and email are managed by system administrators. Please contact an administrator to modify them.'
      }
    },
    game: {
      title: 'Game Management',
      list: 'Game List',
      stats: 'Game Statistics',
      totalGames: 'Total Games',
      enabledGames: 'Enabled Games',
      totalInventory: 'Total Inventory',
      newGames: 'New This Month',
      filterConditions: 'Filter Conditions',
      gameName: 'Game Name',
      gameCode: 'Game Code',
      priceRange: 'Price Range',
      package: 'Package',
      coverImage: 'Cover Image',
      name: 'Name',
      code: 'Code',
      multiLangName: 'Multi-language Names',
      onHand: 'On-hand',
      enabled: 'Enabled',
      supportedLanguages: 'Languages',
      sales: 'Sales',
      price: 'Price',
      actions: 'Actions',
      importGame: 'Import Game',
      batchOperations: 'Batch Operations',
      batchEnable: 'Batch Enable',
      batchDisable: 'Batch Disable',
      batchDelete: 'Batch Delete',
      gameNamePlaceholder: 'Enter game name',
      gameCodePlaceholder: 'Enter game code',
      statusPlaceholder: 'Select status',
      enable: 'Enable',
      disable: 'Disable',
      details: 'Details',
      variants: 'Variants',
      languages: 'languages',
      import: {
        title: 'Import Games',
        searchPlaceholder: 'Search game name',
        codeSearchPlaceholder: 'Search game code',
        selectCategory: 'Select category',
        selectPlatform: 'Select platform',
        importing: 'Importing...',
        importSelected: 'Import Selected Games',
        selectAll: 'Select All Available',
        deselectAll: 'Deselect All',
        selectedCount: '{count} games selected',
        availableGames: 'Available Games',
        category: 'Category',
        platform: 'Platform',
        description: 'Description',
        available: 'Available',
        unavailable: 'Unavailable',
        importSuccess: 'Successfully imported {count} games',
        importFailed: 'Import failed, please try again',
        importing_: 'Importing games...',
        noGamesSelected: 'Please select games to import',
        expandAll: 'Expand All',
        collapseAll: 'Collapse All'
      },
      statistics: {
        title: 'Game Sales Statistics',
        gameBasicInfo: 'Game Basic Information',
        filterConditions: 'Filter Conditions',
        timeRange: 'Time Range',
        region: 'Region',
        currency: 'Currency',
        orderStatus: 'Order Status',
        allRegions: 'All Regions',
        northAmerica: 'North America',
        europe: 'Europe',
        asia: 'Asia',
        others: 'Others',
        allCurrencies: 'All Currencies',
        allStatuses: 'All Statuses',
        completed: 'Completed',
        processing: 'Processing',
        cancelled: 'Cancelled',
        refunded: 'Refunded',
        totalSales: 'Total Sales',
        price: 'Price',
        regionDistribution: 'Sales Distribution by Region',
        regionVolumeDistribution: 'Order Volume Distribution by Region',
        regionAmountDistribution: 'Sales Amount Distribution by Region',
        currencyDistribution: 'Sales Distribution by Currency',
        currencyVolumeDistribution: 'Order Volume Distribution by Currency',
        currencyAmountDistribution: 'Sales Amount Distribution by Currency',
        statusDistribution: 'Order Status Distribution',
        dailySales: 'Daily Sales',
        hourlySales: 'Hourly Sales Trend',
        salesVolume: 'Sales Volume',
        salesAmount: 'Sales Amount',
        orderCount: 'Order Count',
        selectGame: 'Please select a game to view statistics',
        noDataAvailable: 'No data available',
        date: 'Date',
        hour: 'Hour',
        quantity: 'Quantity',
        amount: 'Amount',
        count: 'Count',
        overview: 'Data Overview',
        gameTotalSalesAmount: 'Total Sales Amount',
        gameTotalSalesVolume: 'Total Sales Volume',
        gameRefundCount: 'Refund Count',
        gameRefundRate: 'Refund Rate',
        salesRanking: 'Sales Ranking',
        quantityRanking: 'Quantity Ranking',
        noRanking: 'Not Ranked',
        countries: 'Regions',
        selectCountries: 'Please select regions',
        selectCountry: 'Please select a region'
      },
      orders: {
        title: 'Sales Order Details',
        gameBasicInfo: 'Game Basic Information',
        variantBasicInfo: 'Variant Basic Information',
        filterConditions: 'Filter Conditions',
        region: 'Sales Region',
        distributor: 'Organization',
        dateRange: 'Date Range',
        allRegions: 'All Regions',
        allDistributors: 'All Organizations',
        orderList: 'Order List',
        orderId: 'Order ID',
        orderDate: 'Order Date',
        buyerName: 'Buyer',
        quantity: 'Quantity',
        unitPrice: 'Unit Price',
        totalAmount: 'Total Amount',
        orderStatus: 'Order Status',
        actions: 'Actions',
        viewDetails: 'View Details',
        noOrdersFound: 'No orders found',
        selectGameOrVariant: 'Please select a game or variant to view order details',
        totalOrders: 'Total Orders',
        totalQuantity: 'Total Quantity',
        totalRevenue: 'Total Revenue',
        completed: 'Completed',
        processing: 'Processing',
        cancelled: 'Cancelled',
        refunded: 'Refunded'
      }
    },
    sales: {
      title: 'Sales Statistics',
      stats: 'Sales Statistics',
      charts: 'Sales Charts',
      totalSales: 'Total Sales',
      totalOrders: 'Total Orders',
      todaySales: 'Today Sales',
      monthSales: 'This Month Sales',
      regionDistribution: 'Region Distribution',
      regionVolumeDistribution: 'Order Volume Distribution by Region',
      regionAmountDistribution: 'Sales Amount Distribution by Region',
      currencyDistribution: 'Currency Distribution',
      currencyVolumeDistribution: 'Order Volume Distribution by Currency',
      currencyAmountDistribution: 'Sales Amount Distribution by Currency',
      orderStatus: 'Order Status',
      dailySales: 'Daily Sales Statistics',
      hourlyTrend: 'Hourly Sales Trend',
      exportData: 'Export Data',
      selectRegion: 'Select Region',
      selectCurrency: 'Select Currency',
      allRegions: 'All Regions',
      allCurrencies: 'All Currencies',
      northAmerica: 'North America',
      europe: 'Europe',
      asia: 'Asia',
      others: 'Others',
      completed: 'Completed',
      processing: 'Processing',
      cancelled: 'Cancelled',
      refunded: 'Refunded',
      timeRange: 'Time Range',
      region: 'Region',
      currency: 'Currency',
      tabs: {
        overview: 'Sales Overview',
        orders: 'Order Analysis',
        refunds: 'Refund Analysis',
        regional: 'Regional Analysis',
        customers: 'Customer Analysis',
        topRanking: 'Game Sales Top10'
      },
      topSalesRanking: 'Sales Orders Top10',
      topRefundRanking: 'Refund Orders Top10',
      ordersUnit: 'orders',
      orderCompletionRate: 'Order Completion Rate',
      avgOrderValue: 'Average Order Value',
      orderTimeDistribution: 'Order Time Distribution',
      refundRate: 'Refund Rate',
      refundAmount: 'Refund Amount',
      refundCount: 'Refund Count',
      refundReason: 'Refund Reason',
      refundReasonDistribution: 'Refund Reason Distribution',
      refundRateTrend: 'Refund Rate Trend',
      refundAmountTrend: 'Refund Amount Trend',
      regionalRefundRate: 'Regional Refund Rate Comparison',
      regionalSalesComparison: 'Regional Sales Comparison',
      regionalVolumeComparison: 'Regional Volume Comparison',
      regionalGrowthRate: 'Regional Growth Rate Comparison',
      regionalAvgOrderValue: 'Regional Average Order Value',
      newVsOldUsers: 'New vs Old Users Sales Contribution',
      repurchaseRate: 'User Repurchase Rate Trend',
      orderValueDistribution: 'Order Value Distribution',
      userRegionDistribution: 'User Region Distribution',
      productQualityIssue: 'Product Quality Issue',
      notMeetExpectation: 'Not Meet Expectation',
      technicalIssue: 'Technical Issue',
      priceFactor: 'Price Factor',
      otherReasons: 'Other Reasons',
      newUsers: 'New Users',
      oldUsers: 'Old Users',
      salesAmount: 'Sales Amount',
      orderVolume: 'Order Volume',
      orderCount: 'Order Count',
      salesVsOrders: 'Sales Amount vs Order Count',
      dailySalesTrend: 'Daily Sales Trend',
      refresh: 'Refresh',
      statisticalPeriod: 'Statistical Period',
      monthly: 'Monthly',
      quarterly: 'Quarterly',
      yearly: 'Yearly',
      growthRate: 'Growth Rate',
      userCount: 'User Count',
      orderStatusLabels: {
        completed: 'Completed',
        processing: 'Processing',
        pending: 'Pending',
        cancelled: 'Cancelled'
      },
      paymentStatus: 'Payment Status',
      paymentStatusLabels: {
        completed: 'Payment Completed',
        pending: 'Payment Pending',
        failed: 'Payment Failed',
        cancelled: 'Payment Cancelled'
      },
      refundTrend: 'Refund Trend',
      refundReasons: 'Refund Reasons',
      refundReasonsAnalysis: 'Refund Reasons Analysis',
      productRanking: 'Product Ranking',
      productCode: 'Product Code',
      productSlug: 'Product Slug',
      hour: 'h',
      orderUnit: 'orders',
      peopleUnit: 'people',
      decreaseFromLastMonth: 'Down from last month',
      increaseFromLastMonth: 'Up from last month',
      decreaseOrdersFromLastMonth: 'Decreased from last month',
      noData: 'No Data',
      noDataToExport: 'No data to export',
      exportSuccess: 'Export successful'
    },
    finance: {
      title: 'Financial Reports',
      reports: 'Financial Reports',
      stats: 'Financial Statistics',
      orders: 'Order List',
      totalRevenue: 'Total Revenue',
      monthlyRevenue: 'Monthly Revenue',
      pendingReports: 'Pending Reports',
      publishedReports: 'Published Reports',
      filterConditions: 'Filter Conditions',
      reportId: 'Report ID',
      organization: 'Organization',
      period: 'Period',
      status: 'Status',
      createTime: 'Create Time',
      auditTime: 'Audit Time',
      publishTime: 'Publish Time',
      totalAmount: 'Total Amount',
      actions: 'Actions',
      generateReport: 'Generate Report',
      editReport: 'Edit Financial Report',
      updateReport: 'Update Report',
      editFinancialReport: 'Edit Financial Report',
      generateFinancialReport: 'Generate Financial Report',
      batchAudit: 'Batch Audit',
      items: 'items',
      selectAll: 'Select All',
      clearSelection: 'Clear Selection',
      batchAuditConfirm: 'Confirm batch audit for selected reports?',
      batchAuditSuccess: 'Batch audit completed',
      noReportsSelected: 'Please select reports to audit first',
      noPendingReports: 'No pending reports in the selected reports',
      confirmOperation: 'Confirm Operation',
      view: 'View',
      audit: 'Audit',
      draft: 'Draft',
      pending: 'Pending',
      published: 'Published',
      organizationPlaceholder: 'Enter organization name',
      statusPlaceholder: 'Select status',
      selectMonth: 'Select month',
      // Order related translations
      orderId: 'Order ID',
      orderDate: 'Order Date',
      buyerName: 'Buyer Name',
      distributor: 'Organization',
      gameName: 'Game Name',
      quantity: 'Quantity',
      originalPrice: 'Original Price',
      discount: 'Discount',
      finalPrice: 'Final Price',
      unitPrice: 'Unit Price',
      orderAmount: 'Order Amount',
      orderStatus: 'Order Status',
      selectOrders: 'Select Orders',
      selectedCount: 'Selected {count} orders',
      timeFilter: 'Time Filter',
      thisWeek: 'This Week',
      thisMonth: 'This Month',
      thisQuarter: 'This Quarter',
      thisYear: 'This Year',
      customRange: 'Custom Range',
      distributorFilter: 'Organization Filter',
      allDistributors: 'All Organizations',
      selectDistributor: 'Select Organization',
      distributorPlaceholder: 'Please select organization',
      startDate: 'Start Date',
      endDate: 'End Date',
      selectDateRange: 'Select Date Range',
      resetToOrderDateRange: 'Reset to Order Date Range',
      dateRangeResetSuccess: 'Date range has been reset to order date range',
      // Statistics overview
      totalOrders: 'Total Orders',
      totalQuantity: 'Total Quantity',
      distributors: 'Organizations',
      games: 'Games',
      generateReportFromOrders: 'Generate Report from Selected Orders',
      noOrdersSelected: 'Please select orders to generate report',
      pleaseSelectOrders: 'Please select orders first',
      reportGenerated: 'Report generated successfully',
      reportGenerationFailed: 'Report generation failed',
      reportUpdateSuccess: 'Financial report updated successfully',
      reportGenerateSuccess: 'Financial report generated successfully',
      reportOperationFailed: 'Report operation failed',
      reportTitle: 'Report Title',
      reportType: 'Report Type',
      includeDetails: 'Include Details',
      notes: 'Notes',
      viewOrderDetails: 'View Order Details',
      // Order status
      allStatus: 'All Status',
      orderStatusPlaceholder: 'Select order status',
      completed: 'Completed',
      processing: 'Processing',
      cancelled: 'Cancelled',
      refunded: 'Refunded',
      // Country and currency
      buyerCountry: 'Buyer Country',
      allCountries: 'All Countries',
      countryPlaceholder: 'Select country',
      currency: 'Currency',
      allCurrencies: 'All Currencies',
      currencyPlaceholder: 'Select currency',
      // Report detail related
      reportDetail: 'Report Detail',
      reportBasicInfo: 'Report Basic Information',
      reportName: 'Report Name',
      reportPeriod: 'Report Period',
      reportNotes: 'Notes',
      orderSummary: 'Order Summary',
      gameSummary: 'Game Summary',
      orderDetails: 'Order Details',
      productCode: 'Product Code',
      productName: 'Product Name',
      country: 'Country',
      orderCount: 'Order Count',
      quantitySold: 'Quantity Sold',
      backToReports: 'Back to Reports',
      exportReport: 'Export Report',
      reportSummaryStats: 'Report Summary Statistics',
      // Order detail related translations
      orderDetail: 'Order Detail',
      orderBasicInfo: 'Order Basic Information',
      buyerInfo: 'Buyer Information',
      gameInfo: 'Game Information',
      priceInfo: 'Price Information',
      paymentInfo: 'Payment Information',
      orderNotes: 'Order Notes',
      buyerEmail: 'Email Address',
      buyerPhone: 'Phone Number',
      shippingAddress: 'Shipping Address',
      paymentMethod: 'Payment Method',
      transactionId: 'Transaction ID',
      gameVersion: 'Game Version',
      gameCategory: 'Game Category',
      createdAt: 'Created At',
      updatedAt: 'Updated At',
      // Order items related
      orderItems: 'Order Items',
      itemName: 'Item Name',
      itemType: 'Type',
      itemVersion: 'Version',
      itemCategory: 'Category',
      itemQuantity: 'Quantity',
      itemOriginalPrice: 'Original Price',
      itemDiscount: 'Discount',
      itemFinalPrice: 'Final Price',
      itemTotalAmount: 'Subtotal',
      game: 'Game',
      dlc: 'DLC',
      expansion: 'Expansion',
      // Statistics summary related
      statisticsSummary: 'Statistics Summary',
      totalOrdersCount: 'Total Orders',
      totalAmountSum: 'Total Amount',
      // Sorting related
      sortByDate: 'Sort by Date',
      sortByGame: 'Sort by Game Name',
      sortByCountry: 'Sort by Country',
      ascending: 'Ascending',
      descending: 'Descending',
      // New feature related translations
      addCorrectionRecord: 'Add Correction Record',
      addOrderRecord: 'Add Order Record',
      orderList: 'Order List',
      recordsCount: 'records',
      searchAndAddOrder: 'Search and Add Orders',
      foundOrders: 'Found',
      availableOrders: 'available orders',
      addSelectedOrders: 'Add Selected Orders',
      pleaseSelectOrdersToAdd: 'Please select orders to add',
      orderAddedSuccess: 'Added',
      duplicateOrderWarning: 'Selected orders already exist, no duplicate records added',
      orderDeletedSuccess: 'Order record deleted successfully',
      // Report detail page new translations
      selectedItem: 'Selected Item',
      filteredOrders: 'Filtered Orders',
      allOrders: 'All Orders'
    },
    admin: {
      title: 'Admin Functions',
      settings: 'Admin Settings',
      systemLogs: 'System Logs',
      emailSettings: 'Email Settings',
      reportSettings: 'Report Settings',
      notificationSettings: 'Notification Settings',
      smtpConfig: 'SMTP Configuration',
      smtpHost: 'SMTP Host',
      smtpPort: 'SMTP Port',
      smtpUser: 'Username',
      smtpPassword: 'Password',
      enableTLS: 'Enable TLS',
      emailTemplates: 'Email Templates',
      inviteTemplate: 'Invite Email Template',
      activationTemplate: 'Activation Email Template',
      testConnection: 'Test Connection',
      saveConfig: 'Save Configuration',
      autoGenerate: 'Auto Generate',
      generateDay: 'Generate Day',
      generateTime: 'Generate Time',
      autoAudit: 'Auto Audit',
      autoPublish: 'Auto Publish',
      notifyUsers: 'Notify Users',
      notificationChannels: 'Notification Channels',
      emailNotification: 'Email Notification',
      smsNotification: 'SMS Notification',
      pushNotification: 'Push Notification',
      notificationTypes: 'Notification Types',
      orderNotification: 'Order Notification',
      reportNotification: 'Report Notification',
      systemNotification: 'System Notification',
      operationTime: 'Operation Time',
      operationUser: 'Operation User',
      operationType: 'Operation Type',
      operationContent: 'Operation Content',
      ipAddress: 'IP Address',
      operationResult: 'Operation Result',
      success: 'Success',
      failed: 'Failed',
      exportLogs: 'Export Logs',
      clearLogs: 'Clear Logs',
      userManagement: 'User Management',
      organizationManagement: 'Organization Management',
      gameManagement: 'Game Management',
      login: 'Login',
      systemSettings: 'System Settings',
      timeRange: 'Time Range',
      userPlaceholder: 'Enter username',
      typePlaceholder: 'Select operation type',
      resultPlaceholder: 'Select operation result',
      monthlyDay: 'Day of month',
      // Game import logs related
      gameImportLogs: 'Game Import Logs',
      importLogs: 'Import Logs',
      // Horizon monitor related
      horizon: 'Horizon Monitor',
      taskId: 'Task ID',
      importTime: 'Import Time',
      dataCount: 'Data Count',
      importStatus: 'Import Status',
      retry: 'Retry',
      pending: 'Pending',
      processing: 'Processing',
      completed: 'Completed',
      importFailed: 'Import Failed',
      cancelled: 'Cancelled',
      taskIdPlaceholder: 'Enter task ID',
      statusPlaceholder: 'Select import status',
      retryImport: 'Retry Import',
      retrySuccess: 'Retry task submitted',
      retryFailed: 'Retry failed, please try again later',
      importDetails: 'Import Details',
      totalItems: 'Total Items',
      successItems: 'Success Items',
      failedItems: 'Failed Items',
      errorMessage: 'Error Message',
      importSource: 'Import Source',
      importType: 'Import Type',
      gameImport: 'Game Import',
      variantImport: 'Variant Import',
      priceImport: 'Price Import',
      manualImport: 'Manual Import',
      autoImport: 'Auto Import',
      scheduledImport: 'Scheduled Import',
      totalTasks: 'Total Tasks',
      completedTasks: 'Completed',
      failedTasks: 'Failed',
      successRate: 'Success Rate',
      // Sync progress related translations
      syncProgress: {
        title: 'Sync Progress',
        status: 'Status',
        batchId: 'Batch ID',
        totalRecords: 'Total Records',
        processedRecords: 'Processed',
        successRecords: 'Success',
        failedRecords: 'Failed',
        totalChunks: 'Total Chunks',
        processedChunks: 'Processed Chunks',
        elapsedTime: 'Elapsed Time',
        startTime: 'Start Time',
        estimatedCompletion: 'Estimated Completion',
        loadingProgress: 'Loading sync progress...',
        syncInProgress: 'Syncing data, please wait...',
        close: 'Close',
        runInBackground: 'Run in Background',
        syncStarted: 'Sync Started',
        syncCompleted: 'Sync Completed!',
        syncFailed: 'Sync Failed',
        triggerSyncFailed: 'Failed to Start Sync',
        viewProgress: 'View Progress',
        initializing: 'Initializing sync...',
        syncNotFound: 'Sync task not found or expired',
        stopSync: 'Stop Sync',
        confirmStopSync: 'Are you sure you want to stop the current sync task?',
        syncStopped: 'Sync Stopped',
        statusLabels: {
          pending: 'Pending',
          running: 'Running',
          completed: 'Completed',
          failed: 'Failed'
        }
      },
      // Data sync logs related translations
      dataSyncLogs: 'Data Sync Logs',
      triggerSync: 'Trigger Sync',
      configureTriggerSync: 'Configure Sync',
      startSync: 'Start Sync',
      refresh: 'Refresh',
      startedShort: 'Started',
      recordsShort: 'Records',
      progress: 'Progress',
      viewDetails: 'View Details',
      // View mode related translations
      viewMode: 'View Mode',
      logsMode: 'Logs',
      activeJobsMode: 'Active Jobs',
      syncTypes: {
        product: 'Product',
        inventory: 'Inventory',
        price: 'Price'
      },
      syncTypeFilter: 'Sync Type',
      selectSyncType: 'Select sync type',
      selectStatus: 'Select status',
      reset: 'Reset',
      search: 'Search',
      totalSyncs: 'Total Syncs',
      syncDetails: 'Sync Details',
      close: 'Close',
      completedAt: 'Completed At',
      failedRecordsTitle: 'Failed Records',
      retryStartedSuccess: 'Retry started successfully',
      loadDetailsFailed: 'Failed to load details',
      loadSyncLogsFailed: 'Failed to load sync logs',
      // Active jobs mode related translations
      activeJobs: 'Active Jobs',
      noActiveJobs: 'No active jobs currently running',
      noActiveJobsDesc: 'All sync tasks have been completed or paused',
      loadActiveJobsFailed: 'Failed to load active jobs',
      jobId: 'Job ID',
      autoRefresh: 'Auto Refresh',
      refreshing: 'Refreshing',
      refreshInterval: 'Refresh Interval',
      seconds: 'Seconds',
      // Sync configuration related translations
      syncConfig: {
        title: 'Sync Configuration',
        syncType: 'Sync Type',
        syncMode: 'Sync Mode',
        fullSync: 'Full Sync',
        incrementalSync: 'Incremental Sync',
        batchSize: 'Batch Size',
        timeout: 'Timeout',
        timeoutUnit: 'seconds',
        batchSizeHelp: 'Number of records to process per batch, range: 1-1000',
        timeoutHelp: 'Timeout for sync operation, range: 60-3600 seconds',
        fullSyncHelp: 'Process all data, complete synchronization of all records',
        incrementalSyncHelp: 'Only process data that has changed since the last sync',
        confirm: 'Start Sync',
        cancel: 'Cancel',
        selectSyncType: 'Please select sync type',
        syncTypeOptions: {
          product_sync: 'Product Sync',
          order_sync: 'Order Sync'
        }
      }
    },
    reports: {
      title: 'Reports Management',
      loadFailed: 'Failed to load report data',
      salesDataLoadFailed: 'Failed to load sales data',
      noOrganization: 'No organization information found',
      exportNotImplemented: 'Export function not yet implemented'
    }
  },
  errorHandler: {
    // Common error messages
    operationFailed: 'Operation failed',
    networkError: 'Network connection failed, please check network settings',
    validationFailed: 'Validation failed, {count} errors found',

    // Field display names
    fieldNames: {
      // Organization related fields
      name: 'Organization Name',
      code: 'Organization Code',
      details: 'Organization Details',
      remarks: 'Remarks',
      status: 'Status',

      // User related fields
      email: 'Email',
      password: 'Password',
      password_confirmation: 'Confirm Password',
      username: 'Username',
      user_name: 'Username',
      organisation_ids: 'Organizations',
      role_name: 'Role Name',
      organisation_id: 'Organization ID',
      verification_code: 'Verification Code',

      // Invitation related fields
      role: 'Role',
      model_type: 'Model Type',
      model_id: 'Model ID',
      expires_at: 'Expiration Time',
      max_uses: 'Maximum Uses',
      email_restriction: 'Email Restriction',

      // Common fields
      id: 'ID',
      created_at: 'Created At',
      updated_at: 'Updated At'
    }
  },
  form: {
    required: 'Cannot be empty',
    userName: {
      required: 'Please enter user name',
      invalid: 'User name format is incorrect'
    },
    phone: {
      required: 'Please enter phone number',
      invalid: 'Phone number format is incorrect'
    },
    pwd: {
      required: 'Please enter password',
      invalid: '6-18 characters, including letters, numbers, and underscores'
    },
    confirmPwd: {
      required: 'Please enter password again',
      invalid: 'The two passwords are inconsistent'
    },
    code: {
      required: 'Please enter verification code',
      invalid: 'Verification code format is incorrect'
    },
    email: {
      required: 'Please enter email',
      invalid: 'Email format is incorrect'
    },
    userNameOrEmail: {
      required: 'Please enter username or email',
      invalid: 'Please enter a valid username or email address'
    }
  },
  dropdown: {
    closeCurrent: 'Close Current',
    closeOther: 'Close Other',
    closeLeft: 'Close Left',
    closeRight: 'Close Right',
    closeAll: 'Close All'
  },
  icon: {
    themeConfig: 'Theme Configuration',
    themeSchema: 'Theme Schema',
    lang: 'Switch Language',
    fullscreen: 'Fullscreen',
    fullscreenExit: 'Exit Fullscreen',
    reload: 'Reload Page',
    collapse: 'Collapse Menu',
    expand: 'Expand Menu',
    pin: 'Pin',
    unpin: 'Unpin'
  },
  datatable: {
    itemCount: 'Total {total} items'
  }
};

export default local;
