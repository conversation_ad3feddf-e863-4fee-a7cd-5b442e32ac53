<?php

declare(strict_types=1);

namespace Tests\Feature\Api\V1;

use App\Constants\ErrorCodes;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Organisation;
use App\Models\Product;
use App\Models\Role;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

/**
 * Report Controller Test
 *
 * Tests for report endpoints including permission validation and parameter validation
 */
final class ReportControllerTest extends TestCase
{
    use RefreshDatabase;

    private User $systemRootUser;
    private User $systemAdminUser;
    private User $ownerUser;
    private User $memberUser;
    private User $unrelatedUser;
    private Organisation $organisation;
    private Organisation $anotherOrganisation;
    private Role $systemRootRole;
    private Role $systemAdminRole;
    private Role $ownerRole;
    private Role $memberRole;
    private Product $organisationProduct1;
    private Product $organisationProduct2;
    private Product $anotherOrganisationProduct;

    protected function setUp(): void
    {
        parent::setUp();

        // Create organisations
        $this->organisation = Organisation::factory()->create([
            'name' => 'Test Organisation',
            'status' => 'active',
        ]);

        $this->anotherOrganisation = Organisation::factory()->create([
            'name' => 'Another Organisation',
            'status' => 'active',
        ]);

        // Create system roles
        $this->systemRootRole = Role::firstOrCreate([
            'name' => 'root',
            'guard_name' => 'system',
        ], [
            'organisation_id' => null,
        ]);

        $this->systemAdminRole = Role::firstOrCreate([
            'name' => 'admin',
            'guard_name' => 'system',
        ], [
            'organisation_id' => null,
        ]);

        // Create organisation roles
        $this->ownerRole = Role::firstOrCreate([
            'name' => 'owner',
            'guard_name' => 'api',
            'organisation_id' => $this->organisation->id,
        ]);

        $this->memberRole = Role::firstOrCreate([
            'name' => 'member',
            'guard_name' => 'api',
            'organisation_id' => $this->organisation->id,
        ]);

        // Create users
        $this->systemRootUser = User::factory()->create(['name' => 'System Root']);
        $this->systemAdminUser = User::factory()->create(['name' => 'System Admin']);
        $this->ownerUser = User::factory()->create(['name' => 'Owner User']);
        $this->memberUser = User::factory()->create(['name' => 'Member User']);
        $this->unrelatedUser = User::factory()->create(['name' => 'Unrelated User']);

        // Assign system roles
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId(null);

        // Directly assign role objects to bypass guard checking
        $this->systemRootUser->roles()->attach($this->systemRootRole);
        $this->systemAdminUser->roles()->attach($this->systemAdminRole);

        // Associate users with organisation and assign roles
        $this->ownerUser->organisations()->attach($this->organisation->id);
        $this->memberUser->organisations()->attach($this->organisation->id);

        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($this->organisation->id);
        $this->ownerUser->assignRole($this->ownerRole);
        $this->memberUser->assignRole($this->memberRole);

        // Create test products
        $this->organisationProduct1 = Product::create([
            'owner_id' => $this->organisation->code,
            'store_variant_id' => 1001,
            'name' => 'Organisation Product 1',
            'sku' => 'ORG-PROD-001',
            'code' => 'ORG-PROD-001',
            'slug' => 'organisation-product-1',
            'enabled' => true,
            'current_price' => 2500,
        ]);

        $this->organisationProduct2 = Product::create([
            'owner_id' => $this->organisation->code,
            'store_variant_id' => 1002,
            'name' => 'Organisation Product 2',
            'sku' => 'ORG-PROD-002',
            'code' => 'ORG-PROD-002',
            'slug' => 'organisation-product-2',
            'enabled' => true,
            'current_price' => 3500,
        ]);

        $this->anotherOrganisationProduct = Product::create([
            'owner_id' => $this->anotherOrganisation->code,
            'store_variant_id' => 2001,
            'name' => 'Another Organisation Product',
            'sku' => 'ANOTHER-PROD-001',
            'code' => 'ANOTHER-PROD-001',
            'slug' => 'another-organisation-product',
            'enabled' => true,
            'current_price' => 1500,
        ]);

        // Create test orders for daily sales overview tests
        $this->createDailySalesTestOrders();
    }

    // ========================================
    // Authentication Tests
    // ========================================

    public function test_unauthenticated_user_cannot_access_reports(): void
    {
        $response = $this->getJson('/api/v1/reports/sales');

        $response->assertStatus(401);
    }

    // ========================================
    // Parameter Validation Tests
    // ========================================

    public function test_organisation_id_parameter_is_required_for_non_system_users(): void
    {
        Sanctum::actingAs($this->ownerUser);

        $response = $this->getJson('/api/v1/reports/sales?' . http_build_query([
            'start_date' => '2024-01-01',
            'end_date' => '2024-01-31',
        ]));

        $response->assertStatus(422)
                 ->assertJsonValidationErrors(['organisation_id']);
    }

    public function test_organisation_id_must_be_integer(): void
    {
        Sanctum::actingAs($this->ownerUser);

        $response = $this->getJson('/api/v1/reports/sales?' . http_build_query([
            'start_date' => '2024-01-01',
            'end_date' => '2024-01-31',
            'organisation_id' => 'not-an-integer',
        ]));

        $response->assertStatus(422)
                 ->assertJsonValidationErrors(['organisation_id']);
    }

    public function test_organisation_id_must_be_valid_organisation_id(): void
    {
        Sanctum::actingAs($this->ownerUser);

        $response = $this->getJson('/api/v1/reports/sales?' . http_build_query([
            'start_date' => '2024-01-01',
            'end_date' => '2024-01-31',
            'organisation_id' => 99999, // Non-existent organisation ID
        ]));

        $response->assertStatus(422)
                 ->assertJsonValidationErrors(['organisation_id']);
    }

    public function test_system_users_can_omit_organisation_id(): void
    {
        Sanctum::actingAs($this->systemRootUser);

        $response = $this->getJson('/api/v1/reports/sales?' . http_build_query([
            'start_date' => '2024-01-01',
            'end_date' => '2024-01-31',
        ]));

        $response->assertStatus(200);
    }

    // ========================================
    // Permission Tests - System Users
    // ========================================

    public function test_system_root_can_access_any_organisation(): void
    {
        Sanctum::actingAs($this->systemRootUser);

        $response = $this->getJson('/api/v1/reports/sales?' . http_build_query([
            'start_date' => '2024-01-01',
            'end_date' => '2024-01-31',
            'organisation_id' => $this->organisation->id,
        ]));

        $response->assertStatus(200);
    }

    public function test_system_admin_can_access_any_organisation(): void
    {
        Sanctum::actingAs($this->systemAdminUser);

        $response = $this->getJson('/api/v1/reports/sales?' . http_build_query([
            'start_date' => '2024-01-01',
            'end_date' => '2024-01-31',
            'organisation_id' => $this->anotherOrganisation->id,
        ]));

        $response->assertStatus(200);
    }

    // ========================================
    // Permission Tests - Organisation Users
    // ========================================

    public function test_organisation_owner_can_access_own_organisation(): void
    {
        Sanctum::actingAs($this->ownerUser);

        $response = $this->getJson('/api/v1/reports/sales?' . http_build_query([
            'start_date' => '2024-01-01',
            'end_date' => '2024-01-31',
            'organisation_id' => $this->organisation->id,
        ]));

        $response->assertStatus(200);
    }

    public function test_organisation_member_can_access_own_organisation(): void
    {
        Sanctum::actingAs($this->memberUser);

        $response = $this->getJson('/api/v1/reports/sales?' . http_build_query([
            'start_date' => '2024-01-01',
            'end_date' => '2024-01-31',
            'organisation_id' => $this->organisation->id,
        ]));

        $response->assertStatus(200);
    }



    public function test_organisation_owner_cannot_access_other_organisation(): void
    {
        Sanctum::actingAs($this->ownerUser);

        $response = $this->getJson('/api/v1/reports/sales?' . http_build_query([
            'start_date' => '2024-01-01',
            'end_date' => '2024-01-31',
            'organisation_id' => $this->anotherOrganisation->id,
        ]));

        $response->assertStatus(422)
                 ->assertJsonValidationErrors(['organisation_id']);
    }

    public function test_organisation_member_cannot_access_other_organisation(): void
    {
        Sanctum::actingAs($this->memberUser);

        $response = $this->getJson('/api/v1/reports/sales?' . http_build_query([
            'start_date' => '2024-01-01',
            'end_date' => '2024-01-31',
            'organisation_id' => $this->anotherOrganisation->id,
        ]));

        $response->assertStatus(422)
                 ->assertJsonValidationErrors(['organisation_id']);
    }

    public function test_unrelated_user_cannot_access_any_organisation(): void
    {
        Sanctum::actingAs($this->unrelatedUser);

        $response = $this->getJson('/api/v1/reports/sales?' . http_build_query([
            'start_date' => '2024-01-01',
            'end_date' => '2024-01-31',
            'organisation_id' => $this->organisation->id,
        ]));

        $response->assertStatus(422) // Should be blocked by validation
                 ->assertJsonValidationErrors(['organisation_id']);
    }

    // ========================================
    // All Endpoints Tests
    // ========================================

    public function test_all_report_endpoints_require_organisation_id_for_non_system_users(): void
    {
        Sanctum::actingAs($this->ownerUser);

        $endpoints = [
            '/api/v1/reports/sales',
            '/api/v1/reports/volume',
            '/api/v1/reports/refunds',
            '/api/v1/reports/order-status',
        ];

        foreach ($endpoints as $endpoint) {
            $response = $this->getJson($endpoint . '?' . http_build_query([
                'start_date' => '2024-01-01',
                'end_date' => '2024-01-31',
            ]));

            $response->assertStatus(422)
                     ->assertJsonValidationErrors(['organisation_id']);
        }
    }

    public function test_all_report_endpoints_work_with_valid_organisation_id(): void
    {
        Sanctum::actingAs($this->ownerUser);

        $endpoints = [
            '/api/v1/reports/sales',
            '/api/v1/reports/volume',
            '/api/v1/reports/refunds',
            '/api/v1/reports/order-status',
        ];

        foreach ($endpoints as $endpoint) {
            $response = $this->getJson($endpoint . '?' . http_build_query([
                'start_date' => '2024-01-01',
                'end_date' => '2024-01-31',
                'organisation_id' => $this->organisation->id,
            ]));

            $response->assertStatus(200);
        }
    }

    // ========================================
    // Export Endpoint Tests
    // ========================================

    public function test_export_endpoint_requires_organisation_id_for_non_system_users(): void
    {
        Sanctum::actingAs($this->ownerUser);

        $response = $this->postJson('/api/v1/reports/export', [
            'report_type' => 'sales',
            'format' => 'xlsx',
            'start_date' => '2024-01-01',
            'end_date' => '2024-01-31',
        ]);

        $response->assertStatus(422)
                 ->assertJsonValidationErrors(['organisation_id']);
    }

    public function test_export_endpoint_works_with_valid_organisation_id(): void
    {
        Sanctum::actingAs($this->ownerUser);

        $response = $this->postJson('/api/v1/reports/export', [
            'report_type' => 'sales',
            'format' => 'xlsx',
            'start_date' => '2024-01-01',
            'end_date' => '2024-01-31',
            'organisation_id' => $this->organisation->id,
        ]);

        $response->assertStatus(200);
    }

    public function test_organisation_member_can_export_reports(): void
    {
        Sanctum::actingAs($this->memberUser);

        $response = $this->postJson('/api/v1/reports/export', [
            'report_type' => 'sales',
            'format' => 'xlsx',
            'start_date' => '2024-01-01',
            'end_date' => '2024-01-31',
            'organisation_id' => $this->organisation->id,
        ]);

        $response->assertStatus(200); // Members have same permissions as owners
    }

    public function test_organisation_owner_can_export_reports(): void
    {
        Sanctum::actingAs($this->ownerUser);

        $response = $this->postJson('/api/v1/reports/export', [
            'report_type' => 'sales',
            'format' => 'xlsx',
            'start_date' => '2024-01-01',
            'end_date' => '2024-01-31',
            'organisation_id' => $this->organisation->id,
        ]);

        $response->assertStatus(200);
    }

    // ========================================
    // Report Structure and Internationalization Tests
    // ========================================

    public function test_sales_endpoint_returns_correct_structure(): void
    {
        Sanctum::actingAs($this->ownerUser);

        $response = $this->getJson('/api/v1/reports/sales?' . http_build_query([
            'start_date' => now()->subDays(7)->format('Y-m-d'),
            'end_date' => now()->format('Y-m-d'),
            'group_by' => 'day',
            'organisation_id' => $this->organisation->id,
        ]));

        $response->assertOk()
            ->assertJsonStructure([
                'success',
                'message',
                'data',
                'timestamp',
            ]);

        // Verify the response uses the new Resource structure (no backward compatibility)
        $data = $response->json('data');

        // Should have chart data but not the old raw data structure
        $this->assertArrayHasKey('daily_sales_chart', $data);
        $this->assertArrayHasKey('dual_axis_chart', $data);
        $this->assertArrayHasKey('regional_sales_amount_chart', $data);
    }

    public function test_sales_endpoint_fills_missing_dates_with_zero_values(): void
    {
        Sanctum::actingAs($this->ownerUser);

        // Create an order only for a specific date (far in the past to avoid conflicts with setUp data)
        $specificDate = now()->subDays(30);
        $order = \App\Models\Order::factory()->create([
            'completed_at' => $specificDate,
            'total_amount' => 10000, // $100.00
            'state' => 'completed',
        ]);

        // Add order items to make it valid and link to organisation's product
        \App\Models\OrderItem::factory()->create([
            'order_id' => $order->id,
            'store_variant_id' => $this->organisationProduct1->store_variant_id,
            'quantity' => 1,
            'unit_price' => 10000,
        ]);

        // Request data for a 7-day range around the specific date (avoiding today's data from setUp)
        $startDate = $specificDate->copy()->subDays(3)->format('Y-m-d');
        $endDate = $specificDate->copy()->addDays(3)->format('Y-m-d');

        $response = $this->getJson('/api/v1/reports/sales?' . http_build_query([
            'start_date' => $startDate,
            'end_date' => $endDate,
            'group_by' => 'day',
            'organisation_id' => $this->organisation->id,
        ]));

        $response->assertOk();
        $data = $response->json('data');

        // Verify that daily_sales_chart contains data for all 7 days
        $this->assertArrayHasKey('daily_sales_chart', $data);
        $chartData = $data['daily_sales_chart'];

        // Should have 7 data points (one for each day)
        $this->assertCount(7, $chartData['xAxis']['data']);
        $this->assertCount(7, $chartData['series'][0]['data']);

        // Most days should have zero values, only one day should have actual sales
        $salesData = $chartData['series'][0]['data'];
        $nonZeroCount = count(array_filter($salesData, fn($value) => $value > 0));
        $this->assertEquals(1, $nonZeroCount, 'Should have exactly one day with non-zero sales');

        // Verify that the non-zero value is correct (should be $100)
        $this->assertContains(100, $salesData, 'Should contain the expected sales amount');
    }

    public function test_volume_endpoint_returns_correct_structure(): void
    {
        Sanctum::actingAs($this->ownerUser);

        $response = $this->getJson('/api/v1/reports/volume?' . http_build_query([
            'start_date' => now()->subDays(7)->format('Y-m-d'),
            'end_date' => now()->format('Y-m-d'),
            'group_by' => 'day',
            'organisation_id' => $this->organisation->id,
        ]));

        $response->assertOk()
            ->assertJsonStructure([
                'success',
                'message',
                'data',
                'timestamp',
            ]);

        $data = $response->json('data');

        // Should have chart data specific to volume reports
        $this->assertArrayHasKey('daily_quantity_chart', $data);
        $this->assertArrayHasKey('regional_sales_quantity_chart', $data);
    }

    public function test_volume_endpoint_fills_missing_dates_with_zero_values(): void
    {
        Sanctum::actingAs($this->ownerUser);

        // Create an order only for a specific date (far in the past to avoid conflicts with setUp data)
        $specificDate = now()->subDays(30);
        $order = \App\Models\Order::factory()->create([
            'completed_at' => $specificDate,
            'total_amount' => 5000, // $50.00
            'state' => 'completed',
        ]);

        // Add order items with specific quantity and link to organisation's product
        \App\Models\OrderItem::factory()->create([
            'order_id' => $order->id,
            'store_variant_id' => $this->organisationProduct1->store_variant_id,
            'quantity' => 3,
            'unit_price' => 1667, // Approximately $16.67 each
        ]);

        // Request data for a 5-day range around the specific date (avoiding today's data from setUp)
        $startDate = $specificDate->copy()->subDays(2)->format('Y-m-d');
        $endDate = $specificDate->copy()->addDays(2)->format('Y-m-d');

        $response = $this->getJson('/api/v1/reports/volume?' . http_build_query([
            'start_date' => $startDate,
            'end_date' => $endDate,
            'group_by' => 'day',
            'organisation_id' => $this->organisation->id,
        ]));

        $response->assertOk();
        $data = $response->json('data');

        // Verify that daily_quantity_chart contains data for all 5 days
        $this->assertArrayHasKey('daily_quantity_chart', $data);
        $chartData = $data['daily_quantity_chart'];

        // Should have 5 data points (one for each day)
        $this->assertCount(5, $chartData['xAxis']['data']);
        $this->assertCount(5, $chartData['series'][0]['data']);

        // Most days should have zero values, only one day should have actual quantity
        $quantityData = $chartData['series'][0]['data'];
        $nonZeroCount = count(array_filter($quantityData, fn($value) => $value > 0));
        $this->assertEquals(1, $nonZeroCount, 'Should have exactly one day with non-zero quantity');

        // Verify that the non-zero value is correct (should be 3)
        $this->assertContains(3, $quantityData, 'Should contain the expected quantity');
    }

    public function test_refunds_endpoint_returns_correct_structure(): void
    {
        Sanctum::actingAs($this->ownerUser);

        $response = $this->getJson('/api/v1/reports/refunds?' . http_build_query([
            'start_date' => now()->subDays(7)->format('Y-m-d'),
            'end_date' => now()->format('Y-m-d'),
            'group_by' => 'day',
            'organisation_id' => $this->organisation->id,
        ]));

        $response->assertOk()
            ->assertJsonStructure([
                'success',
                'message',
                'data',
                'timestamp',
            ]);

        $data = $response->json('data');

        // Should have refund-specific chart data
        $this->assertArrayHasKey('refund_trend_chart', $data);
        $this->assertArrayHasKey('refund_reasons_chart', $data);
    }

    public function test_order_status_endpoint_returns_correct_structure(): void
    {
        Sanctum::actingAs($this->ownerUser);

        $response = $this->getJson('/api/v1/reports/order-status?' . http_build_query([
            'start_date' => now()->subDays(7)->format('Y-m-d'),
            'end_date' => now()->format('Y-m-d'),
            'group_by' => 'day',
            'organisation_id' => $this->organisation->id,
        ]));

        $response->assertOk()
            ->assertJsonStructure([
                'success',
                'message',
                'data',
                'timestamp',
            ]);

        $data = $response->json('data');

        // Should have order status specific chart data
        $this->assertArrayHasKey('order_status_chart', $data);
        $this->assertArrayHasKey('payment_status_chart', $data);
    }

    public function test_internationalization_works_with_english(): void
    {
        Sanctum::actingAs($this->ownerUser);

        $response = $this->withHeaders([
            'Accept-Language' => 'en',
        ])->getJson('/api/v1/reports/sales?' . http_build_query([
            'start_date' => now()->subDays(7)->format('Y-m-d'),
            'end_date' => now()->format('Y-m-d'),
            'group_by' => 'day',
            'organisation_id' => $this->organisation->id,
        ]));

        $response->assertOk();

        // Verify English message
        $this->assertEquals('Sales report retrieved successfully', $response->json('message'));
    }

    public function test_internationalization_works_with_chinese(): void
    {
        Sanctum::actingAs($this->ownerUser);

        $response = $this->withHeaders([
            'Accept-Language' => 'zh',
        ])->getJson('/api/v1/reports/sales?' . http_build_query([
            'start_date' => now()->subDays(7)->format('Y-m-d'),
            'end_date' => now()->format('Y-m-d'),
            'group_by' => 'day',
            'organisation_id' => $this->organisation->id,
        ]));

        $response->assertOk();

        // Verify Chinese message
        $this->assertEquals('销售报表获取成功', $response->json('message'));
    }

    public function test_report_endpoints_require_authentication(): void
    {
        $endpoints = [
            '/api/v1/reports/sales',
            '/api/v1/reports/volume',
            '/api/v1/reports/refunds',
            '/api/v1/reports/order-status',
        ];

        foreach ($endpoints as $endpoint) {
            $response = $this->getJson($endpoint);
            $response->assertStatus(401);
        }
    }

    // ========================================
    // Product Filtering Tests
    // ========================================

    public function test_reports_filter_by_organisation_products(): void
    {
        Sanctum::actingAs($this->ownerUser);

        // Create test orders with order items for different organisations
        $organisationOrder = Order::create([
            'store_order_id' => 12345,
            'state' => 'completed',
            'completed_at' => now(),
            'items_total' => 10000,
            'total_amount' => 10000,
            'currency_code' => 'USD',
        ]);

        $anotherOrganisationOrder = Order::create([
            'store_order_id' => 12346,
            'state' => 'completed',
            'completed_at' => now(),
            'items_total' => 5000,
            'total_amount' => 5000,
            'currency_code' => 'USD',
        ]);

        // Create order items for organisation products
        OrderItem::create([
            'order_id' => $organisationOrder->id,
            'store_order_item_id' => 1,
            'store_variant_id' => $this->organisationProduct1->store_variant_id,
            'product_name' => $this->organisationProduct1->name,
            'quantity' => 2,
            'unit_price' => 2500,
            'units_total' => 5000,
            'total' => 5000,
        ]);

        // Create order items for another organisation's products
        OrderItem::create([
            'order_id' => $anotherOrganisationOrder->id,
            'store_order_item_id' => 2,
            'store_variant_id' => $this->anotherOrganisationProduct->store_variant_id,
            'product_name' => $this->anotherOrganisationProduct->name,
            'quantity' => 1,
            'unit_price' => 1500,
            'units_total' => 1500,
            'total' => 1500,
        ]);

        // Test sales report filtering
        $response = $this->getJson('/api/v1/reports/sales?' . http_build_query([
            'start_date' => now()->subDays(1)->format('Y-m-d'),
            'end_date' => now()->format('Y-m-d'),
            'organisation_id' => $this->organisation->id,
        ]));

        $response->assertOk();

        // The response should only include data from the organisation's products
        // This is a basic structure test - the actual filtering logic is tested in service layer
        $this->assertArrayHasKey('data', $response->json());
    }

    public function test_reports_return_empty_when_organisation_has_no_products(): void
    {
        Sanctum::actingAs($this->ownerUser);

        // Create an organisation without products
        $emptyOrganisation = Organisation::factory()->create([
            'name' => 'Empty Organisation',
            'status' => 'active',
        ]);

        // Associate user with the empty organisation
        $this->ownerUser->organisations()->attach($emptyOrganisation->id);

        // Create a role for the user in the empty organisation
        $emptyOrgOwnerRole = Role::firstOrCreate([
            'name' => 'owner',
            'guard_name' => 'api',
            'organisation_id' => $emptyOrganisation->id,
        ]);

        // Assign the role to the user for the empty organisation
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($emptyOrganisation->id);
        $this->ownerUser->assignRole($emptyOrgOwnerRole);

        $response = $this->getJson('/api/v1/reports/sales?' . http_build_query([
            'start_date' => now()->subDays(1)->format('Y-m-d'),
            'end_date' => now()->format('Y-m-d'),
            'organisation_id' => $emptyOrganisation->id,
        ]));

        $response->assertOk();
        $this->assertArrayHasKey('data', $response->json());
    }

    public function test_system_admin_can_access_reports_without_organisation_filter(): void
    {
        Sanctum::actingAs($this->systemAdminUser);

        $response = $this->getJson('/api/v1/reports/sales?' . http_build_query([
            'start_date' => now()->subDays(1)->format('Y-m-d'),
            'end_date' => now()->format('Y-m-d'),
        ]));

        $response->assertOk();
        $this->assertArrayHasKey('data', $response->json());
    }

    // ========================================
    // Product Ranking Report Tests
    // ========================================

    public function test_unauthenticated_user_cannot_access_product_ranking(): void
    {
        $response = $this->getJson('/api/v1/reports/product-ranking');

        $response->assertStatus(401);
    }

    public function test_product_ranking_organisation_id_parameter_is_required_for_non_system_users(): void
    {
        Sanctum::actingAs($this->ownerUser);

        $response = $this->getJson('/api/v1/reports/product-ranking?' . http_build_query([
            'start_date' => '2024-01-01',
            'end_date' => '2024-01-31',
        ]));

        $response->assertStatus(422)
                 ->assertJsonValidationErrors(['organisation_id']);
    }

    public function test_product_ranking_limit_parameter_validation(): void
    {
        Sanctum::actingAs($this->ownerUser);

        // Test invalid limit values
        $invalidLimits = [0, -1, 101, 'invalid'];

        foreach ($invalidLimits as $limit) {
            $response = $this->getJson('/api/v1/reports/product-ranking?' . http_build_query([
                'start_date' => '2024-01-01',
                'end_date' => '2024-01-31',
                'organisation_id' => $this->organisation->id,
                'limit' => $limit,
            ]));

            $response->assertStatus(422)
                     ->assertJsonValidationErrors(['limit']);
        }
    }

    public function test_product_ranking_valid_limit_parameter_is_accepted(): void
    {
        Sanctum::actingAs($this->ownerUser);

        $response = $this->getJson('/api/v1/reports/product-ranking?' . http_build_query([
            'start_date' => now()->subDays(7)->format('Y-m-d'),
            'end_date' => now()->format('Y-m-d'),
            'organisation_id' => $this->organisation->id,
            'limit' => 10,
        ]));

        $response->assertStatus(200);
    }

    public function test_product_ranking_rejects_product_id_parameter(): void
    {
        Sanctum::actingAs($this->ownerUser);

        $response = $this->getJson('/api/v1/reports/product-ranking?' . http_build_query([
            'start_date' => now()->subDays(7)->format('Y-m-d'),
            'end_date' => now()->format('Y-m-d'),
            'organisation_id' => $this->organisation->id,
            'product_id' => 123, // This should be rejected
        ]));

        $response->assertStatus(422)
                 ->assertJsonValidationErrors(['product_id']);
    }

    public function test_product_ranking_default_limit_is_applied_when_not_provided(): void
    {
        Sanctum::actingAs($this->ownerUser);

        $response = $this->getJson('/api/v1/reports/product-ranking?' . http_build_query([
            'start_date' => now()->subDays(7)->format('Y-m-d'),
            'end_date' => now()->format('Y-m-d'),
            'organisation_id' => $this->organisation->id,
        ]));

        $response->assertStatus(200);

        $data = $response->json('data');
        $this->assertEquals(10, $data['summary']['limit']);
    }

    public function test_product_ranking_root_user_can_access_without_organisation_id(): void
    {
        Sanctum::actingAs($this->systemRootUser);

        $response = $this->getJson('/api/v1/reports/product-ranking?' . http_build_query([
            'start_date' => now()->subDays(7)->format('Y-m-d'),
            'end_date' => now()->format('Y-m-d'),
        ]));

        $response->assertStatus(200);
    }

    public function test_product_ranking_admin_user_can_access_without_organisation_id(): void
    {
        Sanctum::actingAs($this->systemAdminUser);

        $response = $this->getJson('/api/v1/reports/product-ranking?' . http_build_query([
            'start_date' => now()->subDays(7)->format('Y-m-d'),
            'end_date' => now()->format('Y-m-d'),
        ]));

        $response->assertStatus(200);
    }

    public function test_product_ranking_organisation_owner_can_access_for_their_organisation(): void
    {
        Sanctum::actingAs($this->ownerUser);

        $response = $this->getJson('/api/v1/reports/product-ranking?' . http_build_query([
            'start_date' => now()->subDays(7)->format('Y-m-d'),
            'end_date' => now()->format('Y-m-d'),
            'organisation_id' => $this->organisation->id,
        ]));

        $response->assertStatus(200);
    }

    public function test_product_ranking_organisation_member_can_access_for_their_organisation(): void
    {
        Sanctum::actingAs($this->memberUser);

        $response = $this->getJson('/api/v1/reports/product-ranking?' . http_build_query([
            'start_date' => now()->subDays(7)->format('Y-m-d'),
            'end_date' => now()->format('Y-m-d'),
            'organisation_id' => $this->organisation->id,
        ]));

        $response->assertStatus(200);
    }

    public function test_product_ranking_returns_correct_structure(): void
    {
        Sanctum::actingAs($this->ownerUser);

        $response = $this->getJson('/api/v1/reports/product-ranking?' . http_build_query([
            'start_date' => now()->subDays(7)->format('Y-m-d'),
            'end_date' => now()->format('Y-m-d'),
            'organisation_id' => $this->organisation->id,
        ]));

        $response->assertOk()
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'sales_rankings' => [
                        '*' => [
                            'rank',
                            'store_variant_id',
                            'product_name',
                            'product_slug',
                            'product_package',
                            'total_quantity',
                            'total_sales',
                            'total_sales_formatted',
                        ]
                    ],
                    'quantity_rankings' => [
                        '*' => [
                            'rank',
                            'store_variant_id',
                            'product_name',
                            'product_slug',
                            'product_package',
                            'total_quantity',
                            'total_sales',
                            'total_sales_formatted',
                        ]
                    ],
                    'sales_ranking_chart' => [
                        'xAxis' => ['data'],
                        'series' => [
                            '*' => [
                                'name',
                                'data'
                            ]
                        ]
                    ],
                    'quantity_ranking_chart' => [
                        'xAxis' => ['data'],
                        'series' => [
                            '*' => [
                                'name',
                                'data'
                            ]
                        ]
                    ],
                    'summary' => [
                        'total_products',
                        'limit',
                        'period'
                    ]
                ],
                'timestamp',
            ]);
    }

    // ========================================
    // Product ID Filtering Tests
    // ========================================

    public function test_product_id_parameter_validation_requires_integer(): void
    {
        Sanctum::actingAs($this->ownerUser);

        $response = $this->getJson('/api/v1/reports/sales?' . http_build_query([
            'start_date' => '2024-01-01',
            'end_date' => '2024-01-31',
            'organisation_id' => $this->organisation->id,
            'product_id' => 'not-an-integer',
        ]));

        $response->assertStatus(422)
                 ->assertJsonValidationErrors(['product_id']);
    }

    public function test_product_id_parameter_validation_requires_existing_product(): void
    {
        Sanctum::actingAs($this->ownerUser);

        $response = $this->getJson('/api/v1/reports/sales?' . http_build_query([
            'start_date' => '2024-01-01',
            'end_date' => '2024-01-31',
            'organisation_id' => $this->organisation->id,
            'product_id' => 99999, // Non-existent product ID
        ]));

        $response->assertStatus(422)
                 ->assertJsonValidationErrors(['product_id']);
    }

    public function test_product_id_parameter_validation_requires_accessible_product(): void
    {
        Sanctum::actingAs($this->ownerUser);

        // Try to access a product from another organisation
        $response = $this->getJson('/api/v1/reports/sales?' . http_build_query([
            'start_date' => '2024-01-01',
            'end_date' => '2024-01-31',
            'organisation_id' => $this->organisation->id,
            'product_id' => $this->anotherOrganisationProduct->store_variant_id,
        ]));

        $response->assertStatus(422)
                 ->assertJsonValidationErrors(['product_id']);
    }

    public function test_sales_report_with_valid_product_id_filter(): void
    {
        Sanctum::actingAs($this->ownerUser);

        $response = $this->getJson('/api/v1/reports/sales?' . http_build_query([
            'start_date' => now()->subDays(7)->format('Y-m-d'),
            'end_date' => now()->format('Y-m-d'),
            'organisation_id' => $this->organisation->id,
            'product_id' => $this->organisationProduct1->store_variant_id,
        ]));

        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'success',
                     'message',
                     'data',
                     'timestamp',
                 ]);
    }

    public function test_volume_report_with_valid_product_id_filter(): void
    {
        Sanctum::actingAs($this->ownerUser);

        $response = $this->getJson('/api/v1/reports/volume?' . http_build_query([
            'start_date' => now()->subDays(7)->format('Y-m-d'),
            'end_date' => now()->format('Y-m-d'),
            'organisation_id' => $this->organisation->id,
            'product_id' => $this->organisationProduct1->store_variant_id,
        ]));

        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'success',
                     'message',
                     'data',
                     'timestamp',
                 ]);
    }

    public function test_refunds_report_with_valid_product_id_filter(): void
    {
        Sanctum::actingAs($this->ownerUser);

        $response = $this->getJson('/api/v1/reports/refunds?' . http_build_query([
            'start_date' => now()->subDays(7)->format('Y-m-d'),
            'end_date' => now()->format('Y-m-d'),
            'organisation_id' => $this->organisation->id,
            'product_id' => $this->organisationProduct1->store_variant_id,
        ]));

        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'success',
                     'message',
                     'data',
                     'timestamp',
                 ]);
    }

    public function test_order_status_report_with_valid_product_id_filter(): void
    {
        Sanctum::actingAs($this->ownerUser);

        $response = $this->getJson('/api/v1/reports/order-status?' . http_build_query([
            'start_date' => now()->subDays(7)->format('Y-m-d'),
            'end_date' => now()->format('Y-m-d'),
            'organisation_id' => $this->organisation->id,
            'product_id' => $this->organisationProduct1->store_variant_id,
        ]));

        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'success',
                     'message',
                     'data',
                     'timestamp',
                 ]);
    }



    public function test_system_admin_can_access_any_product_id(): void
    {
        Sanctum::actingAs($this->systemAdminUser);

        // System admin should be able to access products from any organisation
        $response = $this->getJson('/api/v1/reports/sales?' . http_build_query([
            'start_date' => now()->subDays(7)->format('Y-m-d'),
            'end_date' => now()->format('Y-m-d'),
            'organisation_id' => $this->anotherOrganisation->id,
            'product_id' => $this->anotherOrganisationProduct->store_variant_id,
        ]));

        $response->assertStatus(200);
    }

    public function test_member_user_can_use_product_id_filter_for_accessible_products(): void
    {
        Sanctum::actingAs($this->memberUser);

        $response = $this->getJson('/api/v1/reports/sales?' . http_build_query([
            'start_date' => now()->subDays(7)->format('Y-m-d'),
            'end_date' => now()->format('Y-m-d'),
            'organisation_id' => $this->organisation->id,
            'product_id' => $this->organisationProduct1->store_variant_id,
        ]));

        $response->assertStatus(200);
    }

    public function test_system_admin_can_access_volume_report_without_empty_product_ids_issue(): void
    {
        // This test specifically addresses the bug where admin users would get
        // empty product_ids array, causing "1 = 0" condition in SQL query
        Sanctum::actingAs($this->systemAdminUser);

        $response = $this->getJson('/api/v1/reports/volume?' . http_build_query([
            'start_date' => now()->subDays(7)->format('Y-m-d'),
            'end_date' => now()->format('Y-m-d'),
            'organisation_id' => $this->organisation->id,
        ]));

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'daily_quantity_chart' => [
                        'xAxis' => [
                            'data'
                        ],
                        'series' => [
                            '*' => [
                                'data'
                            ]
                        ]
                    ],
                    'regional_sales_quantity_chart' => [
                        'series' => [
                            '*' => [
                                'data'
                            ]
                        ]
                    ]
                ],
                'message'
            ]);

        // Ensure the response data is not empty (which would indicate the "1 = 0" bug)
        $responseData = $response->json('data');
        $this->assertIsArray($responseData['daily_quantity_chart']);
        $this->assertIsArray($responseData['regional_sales_quantity_chart']);
    }

    /**
     * Test volume report with hourly grouping returns correct time format
     */
    public function test_volume_report_with_hourly_grouping_returns_time_format(): void
    {
        // Create test data with specific hours
        $this->createTestOrdersWithHours();

        Sanctum::actingAs($this->ownerUser);

        // Use yesterday's date to avoid future date validation error
        $testDate = now()->subDay();

        $response = $this->getJson('/api/v1/reports/volume?' . http_build_query([
            'start_date' => $testDate->format('Y-m-d 00:00:00'),
            'end_date' => $testDate->format('Y-m-d 23:59:59'),
            'group_by' => 'hour',
            'organisation_id' => $this->organisation->id,
        ]));

        $response->assertOk()
            ->assertJsonStructure([
                'success',
                'data' => [
                    'daily_quantity_chart' => [
                        'xAxis' => [
                            'data'
                        ],
                        'series' => [
                            [
                                'data'
                            ]
                        ]
                    ],
                    'regional_sales_quantity_chart'
                ],
                'message'
            ]);

        // Verify that the xAxis data contains time format (HH:MM) instead of dates
        $responseData = $response->json('data');
        $xAxisData = $responseData['daily_quantity_chart']['xAxis']['data'];

        if (!empty($xAxisData)) {
            // Check that the first data point follows HH:MM format
            $this->assertMatchesRegularExpression('/^\d{2}:\d{2}$/', $xAxisData[0],
                'Expected time format HH:MM but got: ' . $xAxisData[0]);
        }
    }

    /**
     * Create test orders with specific hours for testing
     */
    private function createTestOrdersWithHours(): void
    {
        $testDate = now()->subDay()->startOfDay();

        // Create orders at different hours of the day
        $hours = [8, 14, 18, 22];

        foreach ($hours as $hour) {
            $orderTime = $testDate->copy()->addHours($hour);

            Order::factory()->create([
                'completed_at' => $orderTime,
                'total_amount' => 5000, // $50.00
                'refund_total' => 0,
                'refund_status' => null,
                'shipping_country' => 'US',
            ]);
        }
    }

    // ========================================
    // Daily Sales Overview Tests
    // ========================================

    public function test_daily_sales_overview_requires_authentication(): void
    {
        $response = $this->getJson('/api/v1/reports/daily-sales-overview?' . http_build_query([
            'organisation_id' => $this->organisation->id,
        ]));

        $response->assertStatus(401);
    }

    public function test_daily_sales_overview_requires_organisation_id_or_product_id(): void
    {
        Sanctum::actingAs($this->ownerUser);

        $response = $this->getJson('/api/v1/reports/daily-sales-overview');

        $response->assertStatus(422)
                 ->assertJsonValidationErrors(['organisation_id']);
    }

    public function test_daily_sales_overview_validates_organisation_exists(): void
    {
        Sanctum::actingAs($this->ownerUser);

        $response = $this->getJson('/api/v1/reports/daily-sales-overview?' . http_build_query([
            'organisation_id' => 99999,
        ]));

        $response->assertStatus(422)
                 ->assertJsonValidationErrors(['organisation_id']);
    }

    public function test_daily_sales_overview_validates_product_exists(): void
    {
        Sanctum::actingAs($this->ownerUser);

        $response = $this->getJson('/api/v1/reports/daily-sales-overview?' . http_build_query([
            'product_id' => 99999,
        ]));

        $response->assertStatus(422)
                 ->assertJsonValidationErrors(['product_id']);
    }

    public function test_daily_sales_overview_validates_timezone_format(): void
    {
        Sanctum::actingAs($this->ownerUser);

        $response = $this->getJson('/api/v1/reports/daily-sales-overview?' . http_build_query([
            'organisation_id' => $this->organisation->id,
            'timezone' => 'Invalid/Timezone',
        ]));

        $response->assertStatus(422)
                 ->assertJsonValidationErrors(['timezone']);
    }

    public function test_system_root_can_access_daily_sales_overview(): void
    {
        Sanctum::actingAs($this->systemRootUser);

        $response = $this->getJson('/api/v1/reports/daily-sales-overview?' . http_build_query([
            'organisation_id' => $this->organisation->id,
        ]));

        $response->assertStatus(200);
    }

    public function test_system_admin_can_access_daily_sales_overview(): void
    {
        Sanctum::actingAs($this->systemAdminUser);

        $response = $this->getJson('/api/v1/reports/daily-sales-overview?' . http_build_query([
            'organisation_id' => $this->organisation->id,
        ]));

        $response->assertStatus(200);
    }

    public function test_organisation_owner_can_access_daily_sales_overview(): void
    {
        Sanctum::actingAs($this->ownerUser);

        $response = $this->getJson('/api/v1/reports/daily-sales-overview?' . http_build_query([
            'organisation_id' => $this->organisation->id,
        ]));

        $response->assertStatus(200);
    }

    public function test_organisation_member_can_access_daily_sales_overview(): void
    {
        Sanctum::actingAs($this->memberUser);

        $response = $this->getJson('/api/v1/reports/daily-sales-overview?' . http_build_query([
            'organisation_id' => $this->organisation->id,
        ]));

        $response->assertStatus(200);
    }

    public function test_organisation_owner_cannot_access_other_organisation_daily_sales_overview(): void
    {
        Sanctum::actingAs($this->ownerUser);

        $response = $this->getJson('/api/v1/reports/daily-sales-overview?' . http_build_query([
            'organisation_id' => $this->anotherOrganisation->id,
        ]));

        // Should return 403 (Forbidden) because it's an authorization issue, not validation
        $response->assertStatus(403);
    }

    public function test_unrelated_user_cannot_access_daily_sales_overview(): void
    {
        Sanctum::actingAs($this->unrelatedUser);

        $response = $this->getJson('/api/v1/reports/daily-sales-overview?' . http_build_query([
            'organisation_id' => $this->organisation->id,
        ]));

        // Should return 403 (Forbidden) because it's an authorization issue, not validation
        $response->assertStatus(403);
    }

    public function test_daily_sales_overview_returns_correct_structure(): void
    {
        Sanctum::actingAs($this->ownerUser);

        $response = $this->getJson('/api/v1/reports/daily-sales-overview?' . http_build_query([
            'organisation_id' => $this->organisation->id,
        ]));

        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'success',
                     'message',
                     'data' => [
                         'overview' => [
                             'sales_amount' => [
                                 'today' => ['value', 'formatted', 'currency'],
                                 'yesterday' => ['value', 'formatted', 'currency'],
                                 'comparison' => ['value', 'trend', 'formatted'],
                             ],
                             'sales_quantity' => [
                                 'today' => ['value'],
                                 'yesterday' => ['value'],
                                 'comparison' => ['value', 'trend', 'formatted'],
                             ],
                             'new_customers' => [
                                 'today' => ['value'],
                                 'yesterday' => ['value'],
                             ],
                             'refunds' => [
                                 'count' => [
                                     'today' => ['value'],
                                     'yesterday' => ['value'],
                                     'comparison' => ['value', 'trend', 'formatted'],
                                 ],
                                 'rate' => [
                                     'today' => ['value', 'formatted'],
                                     'yesterday' => ['value', 'formatted'],
                                     'comparison' => ['value', 'trend', 'formatted'],
                                 ],
                             ],
                         ],
                         'additional_metrics' => [
                             'today' => ['total_orders', 'unique_customers', 'total_refunds'],
                             'yesterday' => ['total_orders', 'unique_customers', 'total_refunds'],
                         ],
                         'meta' => ['date', 'timezone', 'product_id', 'is_product_specific', 'generated_at'],
                     ],
                     'timestamp',
                 ]);
    }

    public function test_daily_sales_overview_supports_product_specific_data(): void
    {
        Sanctum::actingAs($this->ownerUser);

        $response = $this->getJson('/api/v1/reports/daily-sales-overview?' . http_build_query([
            'product_id' => $this->organisationProduct1->store_variant_id,
        ]));

        $response->assertStatus(200)
                 ->assertJsonPath('data.meta.product_id', $this->organisationProduct1->store_variant_id)
                 ->assertJsonPath('data.meta.is_product_specific', true);
    }

    public function test_daily_sales_overview_supports_custom_timezone(): void
    {
        Sanctum::actingAs($this->ownerUser);

        $response = $this->getJson('/api/v1/reports/daily-sales-overview?' . http_build_query([
            'organisation_id' => $this->organisation->id,
            'timezone' => 'America/New_York',
        ]));

        $response->assertStatus(200)
                 ->assertJsonPath('data.meta.timezone', 'America/New_York');
    }

    public function test_daily_sales_overview_validates_product_access_permissions(): void
    {
        Sanctum::actingAs($this->ownerUser);

        // Try to access a product from another organisation
        $response = $this->getJson('/api/v1/reports/daily-sales-overview?' . http_build_query([
            'product_id' => $this->anotherOrganisationProduct->store_variant_id,
        ]));

        // Should return 403 (Forbidden) because it's an authorization issue, not validation
        $response->assertStatus(403);
    }

    public function test_system_admin_can_access_daily_sales_overview_without_organisation_id(): void
    {
        Sanctum::actingAs($this->systemAdminUser);

        $response = $this->getJson('/api/v1/reports/daily-sales-overview?' . http_build_query([
            'product_id' => $this->organisationProduct1->store_variant_id,
        ]));

        $response->assertStatus(200);
    }

    public function test_daily_sales_overview_with_organisation_id_and_product_id_both_provided(): void
    {
        Sanctum::actingAs($this->ownerUser);

        // When both are provided, product_id should take precedence
        $response = $this->getJson('/api/v1/reports/daily-sales-overview?' . http_build_query([
            'organisation_id' => $this->organisation->id,
            'product_id' => $this->organisationProduct1->store_variant_id,
        ]));

        $response->assertStatus(200)
                 ->assertJsonPath('data.meta.product_id', $this->organisationProduct1->store_variant_id)
                 ->assertJsonPath('data.meta.is_product_specific', true);
    }

    public function test_daily_sales_overview_internationalization_works_with_english(): void
    {
        Sanctum::actingAs($this->ownerUser);

        $response = $this->withHeaders([
            'Accept-Language' => 'en',
        ])->getJson('/api/v1/reports/daily-sales-overview?' . http_build_query([
            'organisation_id' => $this->organisation->id,
        ]));

        $response->assertOk();

        // Verify English message (assuming the translation key exists)
        $message = $response->json('message');
        $this->assertIsString($message);
        $this->assertNotEmpty($message);
    }

    public function test_daily_sales_overview_internationalization_works_with_chinese(): void
    {
        Sanctum::actingAs($this->ownerUser);

        $response = $this->withHeaders([
            'Accept-Language' => 'zh',
        ])->getJson('/api/v1/reports/daily-sales-overview?' . http_build_query([
            'organisation_id' => $this->organisation->id,
        ]));

        $response->assertOk();

        // Verify Chinese message (assuming the translation key exists)
        $message = $response->json('message');
        $this->assertIsString($message);
        $this->assertNotEmpty($message);
    }

    /**
     * Create test orders for daily sales overview tests
     */
    private function createDailySalesTestOrders(): void
    {
        $today = now();
        $yesterday = now()->subDay();

        // Today's orders
        $todayOrder1 = Order::create([
            'store_order_id' => 1001,
            'order_number' => 'ORD-TODAY-001',
            'state' => 'completed',
            'completed_at' => $today->copy()->subHours(2),
            'items_total' => 5000, // $50.00
            'adjustments_total' => 0,
            'total_amount' => 5000,
            'currency_code' => 'USD',
            'payment_state' => 'completed',
            'shipping_country' => 'US',
            'customer_id' => 1001,
            'store_updated_at' => $today->copy()->subHours(2),
        ]);

        $todayOrder2 = Order::create([
            'store_order_id' => 1002,
            'order_number' => 'ORD-TODAY-002',
            'state' => 'completed',
            'completed_at' => $today->copy()->subHours(1),
            'items_total' => 3000, // $30.00
            'adjustments_total' => 0,
            'total_amount' => 3000,
            'currency_code' => 'USD',
            'payment_state' => 'completed',
            'shipping_country' => 'US',
            'customer_id' => 1002,
            'refund_status' => 'success',
            'refund_total' => 1000, // $10.00 refund
            'store_updated_at' => $today->copy()->subHours(1),
        ]);

        // Yesterday's orders
        $yesterdayOrder = Order::create([
            'store_order_id' => 1003,
            'order_number' => 'ORD-YESTERDAY-001',
            'state' => 'completed',
            'completed_at' => $yesterday->copy()->subHours(3),
            'items_total' => 4000, // $40.00
            'adjustments_total' => 0,
            'total_amount' => 4000,
            'currency_code' => 'USD',
            'payment_state' => 'completed',
            'shipping_country' => 'US',
            'customer_id' => 1003,
            'store_updated_at' => $yesterday->copy()->subHours(3),
        ]);

        // Create order items
        OrderItem::create([
            'order_id' => $todayOrder1->id,
            'store_order_item_id' => 1001,
            'store_variant_id' => $this->organisationProduct1->store_variant_id,
            'product_name' => $this->organisationProduct1->name,
            'quantity' => 2,
            'unit_price' => 2500,
            'total' => 5000,
            'quantity_refunded' => 0,
        ]);

        OrderItem::create([
            'order_id' => $todayOrder2->id,
            'store_order_item_id' => 1002,
            'store_variant_id' => $this->organisationProduct2->store_variant_id,
            'product_name' => $this->organisationProduct2->name,
            'quantity' => 1,
            'unit_price' => 3000,
            'total' => 3000,
            'quantity_refunded' => 0,
        ]);

        OrderItem::create([
            'order_id' => $yesterdayOrder->id,
            'store_order_item_id' => 1003,
            'store_variant_id' => $this->organisationProduct1->store_variant_id,
            'product_name' => $this->organisationProduct1->name,
            'quantity' => 1,
            'unit_price' => 4000,
            'total' => 4000,
            'quantity_refunded' => 0,
        ]);
    }
}
