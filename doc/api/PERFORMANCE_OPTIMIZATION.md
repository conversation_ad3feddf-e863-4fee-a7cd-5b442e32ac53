# 图表性能优化建议

## 当前实现的优化

### 1. 加载状态管理
- 使用`isLoading`状态避免重复请求
- 显示加载指示器提升用户体验
- 防止用户在数据加载期间进行重复操作

### 2. 错误处理机制
- 统一的错误处理函数`handleReportError`
- 用户友好的错误消息显示
- 网络错误和权限错误的区分处理

### 3. 数据转换优化
- 专门的数据转换函数减少重复计算
- 图表数据格式的标准化处理
- 避免在渲染过程中进行复杂计算

### 4. 组件化设计
- 可复用的composables (`use-reports.ts`)
- 通用的工具函数 (`chart-helpers.ts`)
- 模块化的API服务 (`reports.ts`)

## 建议的进一步优化

### 1. 数据缓存策略

#### 实现内存缓存
```typescript
// 在 use-reports.ts 中添加缓存
const cache = new Map<string, { data: any; timestamp: number }>();
const CACHE_DURATION = 5 * 60 * 1000; // 5分钟

function getCacheKey(params: any): string {
  return JSON.stringify(params);
}

function getCachedData(key: string) {
  const cached = cache.get(key);
  if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
    return cached.data;
  }
  return null;
}

function setCachedData(key: string, data: any) {
  cache.set(key, { data, timestamp: Date.now() });
}
```

#### 智能缓存失效
```typescript
// 当用户切换组织或日期范围时清除相关缓存
function clearRelatedCache(organizationId: number) {
  for (const [key] of cache) {
    if (key.includes(`"organisation_id":${organizationId}`)) {
      cache.delete(key);
    }
  }
}
```

### 2. 请求优化

#### 防抖处理
```typescript
// 在 use-reports.ts 中添加防抖
import { debounce } from 'lodash-es';

const debouncedFetchData = debounce(async (params) => {
  await fetchActualData(params);
}, 300);
```

#### 请求合并
```typescript
// 合并相似的API请求
async function fetchMultipleReports(organizationId: number, dateRange: [number, number]) {
  const params = getDefaultReportParams(organizationId);
  
  // 并行请求多个报表
  const [salesData, orderData, refundData] = await Promise.all([
    fetchSalesReportEnhanced(params),
    fetchOrderStatusReportEnhanced(params),
    fetchRefundsReportEnhanced(params)
  ]);
  
  return { salesData, orderData, refundData };
}
```

### 3. 图表渲染优化

#### 虚拟化长列表
```typescript
// 对于产品排名等长列表，使用虚拟滚动
import { VirtualList } from '@tanstack/vue-virtual';

// 在 top-ranking.vue 中使用虚拟列表
const virtualizedRankings = computed(() => {
  return salesRankings.value.slice(0, 100); // 限制显示数量
});
```

#### 图表懒加载
```typescript
// 使用 Intersection Observer 实现图表懒加载
import { useIntersectionObserver } from '@vueuse/core';

const chartRef = ref<HTMLElement>();
const isVisible = ref(false);

const { stop } = useIntersectionObserver(
  chartRef,
  ([{ isIntersecting }]) => {
    if (isIntersecting && !isVisible.value) {
      isVisible.value = true;
      initChart();
      stop();
    }
  }
);
```

### 4. 内存管理

#### 组件销毁时清理
```typescript
// 在每个图表组件中添加清理逻辑
onBeforeUnmount(() => {
  // 清理图表实例
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }
  
  // 清理定时器
  if (refreshTimer) {
    clearInterval(refreshTimer);
  }
  
  // 清理事件监听器
  window.removeEventListener('resize', handleResize);
});
```

#### 数据清理
```typescript
// 定期清理过期的缓存数据
setInterval(() => {
  const now = Date.now();
  for (const [key, value] of cache) {
    if (now - value.timestamp > CACHE_DURATION) {
      cache.delete(key);
    }
  }
}, 60000); // 每分钟清理一次
```

### 5. 网络优化

#### 请求重试机制
```typescript
async function fetchWithRetry(fetchFn: () => Promise<any>, maxRetries = 3) {
  let lastError;
  
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await fetchFn();
    } catch (error) {
      lastError = error;
      if (i < maxRetries - 1) {
        await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
      }
    }
  }
  
  throw lastError;
}
```

#### 请求超时处理
```typescript
const TIMEOUT_DURATION = 30000; // 30秒

async function fetchWithTimeout(fetchFn: () => Promise<any>) {
  const timeoutPromise = new Promise((_, reject) => {
    setTimeout(() => reject(new Error('Request timeout')), TIMEOUT_DURATION);
  });
  
  return Promise.race([fetchFn(), timeoutPromise]);
}
```

### 6. 用户体验优化

#### 骨架屏
```vue
<!-- 在图表组件中使用骨架屏 -->
<template>
  <div v-if="isLoading" class="chart-skeleton">
    <NSkeleton height="300px" />
  </div>
  <div v-else ref="chartRef" class="chart-container"></div>
</template>
```

#### 渐进式加载
```typescript
// 先显示基础数据，再加载详细数据
async function loadDataProgressively() {
  // 第一步：加载基础统计数据
  const basicData = await fetchBasicStats();
  updateBasicCharts(basicData);
  
  // 第二步：加载详细图表数据
  const detailedData = await fetchDetailedCharts();
  updateDetailedCharts(detailedData);
}
```

### 7. 监控和分析

#### 性能监控
```typescript
// 添加性能监控
function trackApiPerformance(apiName: string, startTime: number) {
  const duration = Date.now() - startTime;
  console.log(`API ${apiName} took ${duration}ms`);
  
  // 发送到监控服务
  if (duration > 5000) {
    console.warn(`Slow API detected: ${apiName} took ${duration}ms`);
  }
}
```

#### 错误追踪
```typescript
// 统一的错误追踪
function trackError(error: Error, context: string) {
  console.error(`Error in ${context}:`, error);
  
  // 发送到错误追踪服务
  // errorTrackingService.captureException(error, { context });
}
```

## 实施优先级

### 高优先级
1. 数据缓存机制
2. 请求防抖处理
3. 组件销毁时的内存清理

### 中优先级
1. 图表懒加载
2. 请求重试机制
3. 骨架屏优化

### 低优先级
1. 虚拟化长列表
2. 性能监控
3. 渐进式加载

## 性能指标

### 目标指标
- 首次内容绘制 (FCP): < 1.5秒
- 最大内容绘制 (LCP): < 2.5秒
- 首次输入延迟 (FID): < 100毫秒
- 累积布局偏移 (CLS): < 0.1

### 监控方法
```typescript
// 使用 Performance API 监控
const observer = new PerformanceObserver((list) => {
  for (const entry of list.getEntries()) {
    console.log(`${entry.name}: ${entry.duration}ms`);
  }
});

observer.observe({ entryTypes: ['measure', 'navigation'] });
```

## 总结

通过实施这些优化策略，可以显著提升图表组件的性能和用户体验。建议按照优先级逐步实施，并持续监控性能指标以确保优化效果。
