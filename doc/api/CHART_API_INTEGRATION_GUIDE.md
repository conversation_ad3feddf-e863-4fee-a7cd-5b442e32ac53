# 图表API集成指南

本文档说明了如何测试和使用已集成的图表API功能。

## 已完成的集成

### 1. 销售综合图表 (sales-charts.vue)
- **API端点**: `/api/v1/reports/sales`
- **功能**: 
  - 地区销售分布饼图
  - 销售金额与订单数量双轴图
  - 日销售趋势图
- **支持的筛选**: 日期范围、货币、地区

### 2. 销售统计卡片 (sales-stats.vue)
- **API端点**: `/api/v1/reports/sales`
- **功能**: 
  - 总销售额
  - 总订单数
  - 今日销售额
  - 本月销售额
- **自动更新**: 根据传入的日期范围和货币参数

### 3. 订单分析图表 (order-analysis.vue)
- **API端点**: `/api/v1/reports/order-status`
- **功能**: 
  - 订单状态分布饼图
  - 支付状态分布饼图
- **支持的筛选**: 日期范围、货币、地区

### 4. 退款分析图表 (refund-analysis.vue)
- **API端点**: `/api/v1/reports/refunds`
- **功能**: 
  - 退款趋势图（金额和数量双轴）
- **支持的筛选**: 日期范围、货币、地区

### 5. 产品排名组件 (top-ranking.vue)
- **API端点**: `/api/v1/reports/product-ranking`
- **功能**: 
  - 销售金额排名TOP10
  - 销售数量排名TOP10
- **支持的筛选**: 日期范围、货币

### 6. 首页统计图表
- **已集成的组件**:
  - 统计卡片 (card-data.vue) - 部分集成
  - 日销售额图表 (daily-sales-amount-chart.vue)
  - 日销售量图表 (daily-sales-quantity-chart.vue)
  - 地区销售额分布 (regional-sales-amount-chart.vue)
  - 地区销售量分布 (regional-sales-quantity-chart.vue)
  - 销售金额排名 (top-sales-amount-ranking.vue)
  - 销售数量排名 (top-sales-quantity-ranking.vue)

## 使用方法

### 1. 访问销售页面
```
http://localhost:9527/cmc/sales
```

### 2. 测试功能
1. **日期范围选择**: 在页面顶部选择不同的日期范围
2. **货币切换**: 选择不同的货币（USD、EUR、CNY）
3. **地区筛选**: 选择不同的地区进行数据筛选
4. **刷新数据**: 点击刷新按钮重新加载数据

### 3. 检查数据加载
- 图表应显示加载状态（骨架屏或加载指示器）
- 数据加载完成后显示真实的图表内容
- 如果没有数据，应显示"暂无数据"提示

## 错误处理

### 1. 网络错误
- 显示用户友好的错误消息
- 自动重试机制（可选）

### 2. 权限错误
- 检查用户是否有访问组织数据的权限
- 显示相应的权限错误提示

### 3. 数据验证错误
- 检查API参数是否正确
- 显示参数验证错误信息

## 性能优化

### 1. 加载状态管理
- 使用`isLoading`状态避免重复请求
- 显示适当的加载指示器

### 2. 错误边界
- 组件级别的错误处理
- 全局错误处理机制

### 3. 数据缓存
- 避免重复的API调用
- 智能的数据更新策略

## 开发工具

### 1. 通用工具函数 (use-reports.ts)
```typescript
import { useReports } from '@/composables/use-reports';

const {
  isLoading,
  defaultOrganizationId,
  formatDateRangeForApi,
  handleReportError
} = useReports();
```

### 2. 图表数据处理 (chart-helpers.ts)
```typescript
import { 
  transformSalesChartData,
  formatCurrency,
  formatNumber 
} from '@/utils/chart-helpers';
```

### 3. 增强的API函数 (reports.ts)
```typescript
import { 
  fetchSalesReportEnhanced,
  fetchOrderStatusReportEnhanced 
} from '@/service/api/reports';
```

## 故障排除

### 1. 图表不显示数据
- 检查网络请求是否成功
- 验证API响应数据格式
- 检查组织ID是否正确

### 2. 加载状态异常
- 检查`isLoading`状态管理
- 验证错误处理逻辑

### 3. 国际化问题
- 检查国际化键值是否正确
- 验证语言切换功能

## API参数说明

### 通用参数
- `start_date`: 开始日期 (YYYY-MM-DD)
- `end_date`: 结束日期 (YYYY-MM-DD)
- `organisation_id`: 组织ID (必填)
- `group_by`: 分组方式 ('day', 'week', 'month')
- `currency`: 货币代码 ('USD', 'EUR', 'CNY')
- `countries`: 国家代码数组 (可选)

### 产品排名特有参数
- `limit`: 返回记录数量限制 (默认10)

## 注意事项

1. **组织权限**: 确保用户有访问所选组织数据的权限
2. **日期范围**: 避免选择过大的日期范围影响性能
3. **错误处理**: 所有API调用都应有适当的错误处理
4. **加载状态**: 提供良好的用户体验，显示加载状态
5. **数据验证**: 验证API返回的数据格式和完整性

## 后续改进

1. **数据缓存**: 实现智能的数据缓存机制
2. **实时更新**: 添加数据自动刷新功能
3. **导出功能**: 完善数据导出功能
4. **更多筛选**: 添加更多的数据筛选选项
5. **性能监控**: 添加API性能监控和优化

