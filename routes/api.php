<?php

declare(strict_types=1);

use App\Http\Controllers\Api\V1\AuthController;
use App\Http\Controllers\Api\V1\InvitationController;
use App\Http\Controllers\Api\V1\OrganisationController;
use App\Http\Controllers\Api\V1\ProductPermissionController;
use App\Http\Controllers\Api\V1\ReportController;
use App\Http\Controllers\Api\V1\RoleController;
use App\Http\Controllers\Api\V1\StatusController;
use App\Http\Controllers\Api\V1\SyncController;
use App\Http\Controllers\Api\V1\UserController;
use App\Http\Controllers\Api\V1\UserRoleController;
use App\Services\PermissionService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Global health check endpoint (outside versioning)
Route::get('/health', [StatusController::class, 'health']);

// API version 1 routes
Route::prefix('v1')->name('api.v1.')->group(function () {
    // Public API routes
    Route::get('/status', [StatusController::class, 'index'])->name('status');
    Route::get('/health', [StatusController::class, 'health'])->name('health');

    // Authentication routes
    Route::prefix('auth')->name('auth.')->group(function () {
        // Public authentication routes
        Route::post('/login', [AuthController::class, 'login'])->name('login');

        // Protected authentication routes (require authentication)
        Route::middleware('auth:sanctum')->group(function () {
            Route::post('/logout', [AuthController::class, 'logout'])->name('logout');
            Route::post('/revoke-all-tokens', [AuthController::class, 'revokeAllTokens'])->name('revoke-all-tokens');
        });
    });

    // User management routes
    Route::prefix('users')->name('users.')->group(function () {
        // Public user registration routes
        Route::post('/send-verification-code', [UserController::class, 'sendVerificationCode'])->name('send-verification-code');
        Route::post('/register', [UserController::class, 'register'])->name('register');

        // Protected user routes (require authentication)
        Route::middleware('auth:sanctum')->group(function () {
            // User role management routes (must come before apiResource to avoid conflicts)
            // Get assignable roles for current user
            Route::get('/assignable-roles', [UserRoleController::class, 'getAssignableRoles'])->name('assignable-roles');

            // User role operations
            Route::post('/{user}/roles', [UserRoleController::class, 'assignRole'])->name('assign-role');
            Route::delete('/{user}/roles/{role}', [UserRoleController::class, 'removeRole'])->name('remove-role');
            Route::get('/{user}/roles', [UserRoleController::class, 'getUserRoles'])->name('get-roles');

            // Owner role transfer (special case)
            Route::put('/{user}/transfer-owner', [UserRoleController::class, 'transferOwnerRole'])->name('transfer-owner');

            // User CRUD routes - accessible by system admins and organisation owners
            Route::get('/', [UserController::class, 'index'])->name('index');
            Route::get('/{user}', [UserController::class, 'show'])->name('show');
            Route::post('/', [UserController::class, 'store'])->name('store');
            Route::put('/{user}', [UserController::class, 'update'])->name('update');
            Route::patch('/{user}', [UserController::class, 'update'])->name('update');
            Route::post('/{user}/suspend', [UserController::class, 'suspend'])->name('suspend');
            Route::post('/{user}/activate', [UserController::class, 'activate'])->name('activate');

            // User-Organisation association routes
            Route::post('/{userId}/organisations/{organisationId}', [UserController::class, 'addToOrganisation'])->name('add-to-organisation');
            Route::delete('/{userId}/organisations/{organisationId}', [UserController::class, 'removeFromOrganisation'])->name('remove-from-organisation');
            Route::put('/{userId}/organisations', [UserController::class, 'syncOrganisations'])->name('sync-organisations');

            // User product permission management routes
            // Get user's product permissions
            Route::get('/{user}/product-permissions', [ProductPermissionController::class, 'getUserProductPermissions'])->name('product-permissions');

            // Bulk product permission operations
            Route::post('/{user}/product-permissions/grant-multiple', [ProductPermissionController::class, 'grantMultipleAccess'])->name('grant-multiple-permissions');
            Route::delete('/{user}/product-permissions/revoke-multiple', [ProductPermissionController::class, 'revokeMultipleAccess'])->name('revoke-multiple-permissions');
        });
    });

    // Invitation routes
    Route::prefix('invitations')->name('invitations.')->group(function () {
        // List invitations - requires authentication and proper permissions
        Route::get('/', [InvitationController::class, 'index'])->name('index')->middleware('auth:sanctum');
        // Show invitation info - can be accessed without authentication, but will return 401 if not logged in
        Route::get('/{invitation}', [InvitationController::class, 'show'])->name('show');
        // Accept invitation - requires authentication
        Route::post('/{invitation}/accept', [InvitationController::class, 'accept'])->name('accept')->middleware('auth:sanctum');
        // Create invitation - requires authentication
        Route::post('/', [InvitationController::class, 'store'])->name('store')->middleware('auth:sanctum');
    });

    // Protected routes (require authentication)
    Route::middleware('auth:sanctum')->group(function () {
        // User information route with roles
        Route::get('/user', [UserController::class, 'getCurrentUser'])->name('user');

        // User profile management routes (self-service)
        Route::put('/user/profile', [UserController::class, 'updateProfile'])->name('user.update-profile');
        Route::put('/user/password', [UserController::class, 'changePassword'])->name('user.change-password');
        Route::post('/user/avatar', [UserController::class, 'uploadAvatar'])->name('user.upload-avatar');

        // Organisation management routes
        Route::apiResource('organisations', OrganisationController::class)->except(['destroy']);
        Route::post('/organisations/{organisation}/suspend', [OrganisationController::class, 'suspend'])->name('organisations.suspend');

        // Role management routes - restricted to Root and Admin roles
        Route::middleware(['admin'])->group(function () {
            Route::get('/roles', [RoleController::class, 'index'])->name('roles.index');
            Route::get('/roles/{role}', [RoleController::class, 'show'])->name('roles.show');
            Route::post('/roles', [RoleController::class, 'store'])->name('roles.store');
            Route::put('/roles/{role}', [RoleController::class, 'update'])->name('roles.update');
            Route::delete('/roles/{role}', [RoleController::class, 'destroy'])->name('roles.destroy');
        });

        // Sync management routes - restricted to system administrators
        Route::prefix('sync')->name('sync.')->group(function () {
            Route::get('/logs', [SyncController::class, 'index'])->name('logs');
            Route::get('/logs/{syncLog}', [SyncController::class, 'show'])->name('show');
            Route::post('/trigger', [SyncController::class, 'trigger'])->name('trigger');
            Route::post('/retry/{syncLog}', [SyncController::class, 'retry'])->name('retry');
            Route::post('/revalidate/{syncLog}', [SyncController::class, 'revalidate'])->name('revalidate');
            Route::post('/batch-revalidate', [SyncController::class, 'batchRevalidate'])->name('batch-revalidate');
            Route::get('/revalidation-candidates', [SyncController::class, 'revalidationCandidates'])->name('revalidation-candidates');
            Route::get('/progress', [SyncController::class, 'progress'])->name('progress');
            Route::get('/active-jobs', [SyncController::class, 'activeJobs'])->name('active-jobs');
            Route::post('/cleanup-job', [SyncController::class, 'cleanupJob'])->name('cleanup-job');
            Route::get('/job-details', [SyncController::class, 'jobDetails'])->name('job-details');
        });

        // Report management routes - accessible by organization members and above
        Route::prefix('reports')->name('reports.')->group(function () {
            Route::get('/sales', [ReportController::class, 'sales'])->name('sales');
            Route::get('/volume', [ReportController::class, 'volume'])->name('volume');
            Route::get('/refunds', [ReportController::class, 'refunds'])->name('refunds');
            Route::get('/order-status', [ReportController::class, 'orderStatus'])->name('order-status');
            Route::get('/product-ranking', [ReportController::class, 'productRanking'])->name('product-ranking');
            Route::get('/daily-sales-overview', [ReportController::class, 'dailySalesOverview'])->name('daily-sales-overview');
            Route::post('/export', [ReportController::class, 'export'])->name('export');
        });

        // Product permission management routes - accessible by system admins and organization owners
        Route::prefix('products')->name('products.')->group(function () {
            // Grant/revoke product access for individual products
            Route::post('/{product}/permissions', [ProductPermissionController::class, 'grantAccess'])->name('grant-access');
            Route::delete('/{product}/permissions/{user}', [ProductPermissionController::class, 'revokeAccess'])->name('revoke-access');

            // Get authorized users for a product
            Route::get('/{product}/authorized-users', [ProductPermissionController::class, 'getAuthorizedUsers'])->name('authorized-users');

            // Get current user's accessible products
            Route::get('/accessible', [ProductPermissionController::class, 'getUserAccessibleProducts'])->name('accessible');
        });

        // Bulk product permission operations (alternative endpoint structure)
        Route::prefix('product-permissions')->name('product-permissions.')->group(function () {
            Route::post('/grant-multiple', [ProductPermissionController::class, 'grantMultipleAccess'])->name('grant-multiple');

            // Get organisation product permissions grouped by users
            Route::get('/organisation', [ProductPermissionController::class, 'getOrganisationProductPermissions'])->name('organisation');
        });

    });

    // Test route for SQL logging and performance monitoring (only in local environment)
    if (app()->environment('local')) {
        Route::get('/test-sql-logging', function (Request $request) {
            $data = [];
            $testType = $request->query('test', 'basic');

            switch ($testType) {
                case 'performance':
                    // Performance test with multiple queries
                    $data['user_count'] = \App\Models\User::count();
                    $data['verified_users'] = \App\Models\User::whereNotNull('email_verified_at')->count();
                    $data['roles'] = \App\Models\Role::where('guard_name', 'api')->get(['id', 'name', 'organisation_id']);
                    $data['organisations'] = \App\Models\Organisation::all(['id', 'name', 'status']);

                    // Simulate some processing time
                    usleep(50000); // 50ms delay

                    // More complex queries
                    $data['user_roles'] = \App\Models\User::with('roles')->limit(10)->get(['id', 'name', 'email']);
                    $data['recent_users'] = \DB::select('SELECT COUNT(*) as total FROM users WHERE created_at > ?', [now()->subDays(30)])[0]->total ?? 0;
                    break;

                case 'slow':
                    // Simulate slow endpoint
                    $data['message'] = 'Simulating slow endpoint';
                    usleep(200000); // 200ms delay
                    $data['users'] = \App\Models\User::limit(5)->get();
                    usleep(100000); // Additional 100ms delay
                    break;

                case 'heavy':
                    // Heavy database operations
                    $data['all_users'] = \App\Models\User::with(['roles', 'organisations'])->limit(20)->get();
                    $data['all_roles'] = \App\Models\Role::with('users')->get();
                    $data['statistics'] = [
                        'total_users' => \App\Models\User::count(),
                        'total_roles' => \App\Models\Role::count(),
                        'total_orgs' => \App\Models\Organisation::count(),
                    ];
                    break;

                default:
                    // Basic test
                    $data['user_count'] = \App\Models\User::count();
                    $data['verified_users'] = \App\Models\User::whereNotNull('email_verified_at')->count();
                    $data['roles'] = \App\Models\Role::where('guard_name', 'api')->get(['id', 'name', 'organisation_id']);
                    $data['organisations'] = \App\Models\Organisation::all(['id', 'name', 'status']);
                    $data['recent_users'] = \DB::select('SELECT COUNT(*) as total FROM users WHERE created_at > ?', [now()->subDays(30)])[0]->total ?? 0;
            }

            return response()->json([
                'success' => true,
                'message' => "SQL logging test completed - {$testType} mode",
                'test_type' => $testType,
                'data' => $data,
                'request_info' => [
                    'method' => $request->getMethod(),
                    'url' => $request->fullUrl(),
                    'query_params' => $request->query->all(),
                ]
            ]);
        })->name('test-sql-logging');
    }

    // Add more API routes here as needed
});
