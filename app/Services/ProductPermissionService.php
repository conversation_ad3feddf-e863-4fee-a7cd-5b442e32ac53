<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\Product;
use App\Models\ProductPermission;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use InvalidArgumentException;

final readonly class ProductPermissionService
{
    /**
     * Grant product access to a user.
     */
    public function grantProductAccess(
        User $user,
        Product $product,
        string $permissionType = 'view-reports',
        ?User $grantedBy = null,
        ?Carbon $expiresAt = null,
        ?string $notes = null
    ): ProductPermission {
        // Check if product exists
        if (!$product->exists) {
            throw new InvalidArgumentException('Product does not exist');
        }

        return $product->grantAccessToUser($user, $permissionType, $grantedBy, $expiresAt, $notes);
    }

    /**
     * Revoke product access from a user.
     */
    public function revokeProductAccess(User $user, Product $product, string $permissionType = 'view-reports'): bool
    {
        return $product->revokeAccessFromUser($user, $permissionType);
    }

    /**
     * Get products accessible by a user.
     */
    public function getUserAccessibleProducts(User $user, string $permissionType = 'view-reports'): Collection
    {
        $productIds = $user->getProductPermissionIds();
        return Product::whereIn('id', $productIds)
            ->with('organisation')
            ->get();
    }

    /**
     * Get users authorized for a product.
     */
    public function getProductAuthorizedUsers(Product $product, string $permissionType = 'view-reports'): Collection
    {
        return $product->getAuthorizedUsers($permissionType);
    }

    /**
     * Grant multiple product access to a user.
     */
    public function grantMultipleProductAccess(
        User $user,
        array $productIds,
        string $permissionType = 'view-reports',
        ?User $grantedBy = null,
        ?Carbon $expiresAt = null,
        ?string $notes = null
    ): Collection {
        $permissions = collect();

        foreach ($productIds as $productId) {
            $product = Product::find($productId);
            if ($product) {
                $permission = $this->grantProductAccess($user, $product, $permissionType, $grantedBy, $expiresAt, $notes);
                $permissions->push($permission);
            }
        }

        return $permissions;
    }

    /**
     * Revoke multiple product access from a user.
     */
    public function revokeMultipleProductAccess(User $user, array $productIds, string $permissionType = 'view-reports'): int
    {
        return ProductPermission::where('user_id', $user->id)
            ->whereIn('product_id', $productIds)
            ->where('permission_type', $permissionType)
            ->delete();
    }

    /**
     * Check if user has product access.
     */
    public function userHasProductAccess(User $user, Product $product, string $permissionType = 'view-reports'): bool
    {
        return $product->userHasAccess($user, $permissionType);
    }

    /**
     * Get permissions expiring within specified days.
     */
    public function getExpiringPermissions(int $days = 7): Collection
    {
        return ProductPermission::with(['user', 'product'])
            ->where('expires_at', '<=', now()->addDays($days))
            ->where('expires_at', '>', now())
            ->get();
    }

    /**
     * Clean up expired permissions.
     */
    public function cleanupExpiredPermissions(): int
    {
        return ProductPermission::where('expires_at', '<=', now())->delete();
    }

    /**
     * Get user's product permissions within a specific organisation.
     */
    public function getUserProductPermissionsInOrganisation(User $user, int $organisationId): Collection
    {
        return ProductPermission::with(['product'])
            ->where('user_id', $user->id)
            ->whereHas('product', function ($query) use ($organisationId) {
                $query->whereHas('organisation', function ($subQuery) use ($organisationId) {
                    $subQuery->where('organisations.id', $organisationId);
                });
            })
            ->valid()
            ->get();
    }

    /**
     * Get permission statistics for a user.
     */
    public function getUserPermissionStats(User $user): array
    {
        $permissions = $user->productPermissions()->valid()->get();

        return [
            'total_permissions' => $permissions->count(),
            'by_type' => $permissions->groupBy('permission_type')->map->count(),
            'expiring_soon' => $permissions->filter(function ($permission) {
                return $permission->expires_at && $permission->expires_at->diffInDays(now()) <= 7;
            })->count(),
            'organisations' => $permissions->load('product.organisation')
                ->pluck('product.organisation')
                ->filter()
                ->unique('id')
                ->count(),
        ];
    }

    /**
     * Get permission statistics for a product.
     */
    public function getProductPermissionStats(Product $product): array
    {
        $permissions = $product->productPermissions()->valid()->get();

        return [
            'total_permissions' => $permissions->count(),
            'by_type' => $permissions->groupBy('permission_type')->map->count(),
            'unique_users' => $permissions->pluck('user_id')->unique()->count(),
            'expiring_soon' => $permissions->filter(function ($permission) {
                return $permission->expires_at && $permission->expires_at->diffInDays(now()) <= 7;
            })->count(),
        ];
    }

    /**
     * Bulk update permission expiration dates.
     */
    public function bulkUpdateExpirationDates(array $permissionIds, ?Carbon $expiresAt): int
    {
        return ProductPermission::whereIn('id', $permissionIds)
            ->update(['expires_at' => $expiresAt]);
    }

    /**
     * Get all permissions for an organisation's products.
     */
    public function getOrganisationProductPermissions(int $organisationId): Collection
    {
        return ProductPermission::with(['user', 'product', 'grantedBy'])
            ->whereHas('product', function ($query) use ($organisationId) {
                $query->whereHas('organisation', function ($subQuery) use ($organisationId) {
                    $subQuery->where('organisations.id', $organisationId);
                });
            })
            ->valid()
            ->get();
    }

    /**
     * Get all permissions for an organisation's products including expired ones.
     */
    public function getOrganisationProductPermissionsIncludingExpired(int $organisationId): Collection
    {
        return ProductPermission::with(['user', 'product', 'grantedBy'])
            ->whereHas('product', function ($query) use ($organisationId) {
                $query->whereHas('organisation', function ($subQuery) use ($organisationId) {
                    $subQuery->where('organisations.id', $organisationId);
                });
            })
            ->get();
    }

    /**
     * Get organisation product permissions grouped by users.
     * Returns permissions organized by user, showing which products each user has access to.
     */
    public function getOrganisationProductPermissionsGroupedByUsers(int $organisationId, bool $includeExpired = false): Collection
    {
        $permissions = $includeExpired
            ? $this->getOrganisationProductPermissionsIncludingExpired($organisationId)
            : $this->getOrganisationProductPermissions($organisationId);

        return $permissions->groupBy('user_id')->map(function ($userPermissions, $userId) {
            $user = $userPermissions->first()->user;

            return [
                'user' => [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                ],
                'permissions' => $userPermissions->map(function ($permission) {
                    return [
                        'id' => $permission->id,
                        'product' => [
                            'id' => $permission->product->id,
                            'name' => $permission->product->name,
                            'slug' => $permission->product->slug,
                            'code' => $permission->product->code,
                        ],
                        'permission_type' => $permission->permission_type,
                        'expires_at' => $permission->expires_at?->toISOString(),
                        'granted_at' => $permission->granted_at->toISOString(),
                        'granted_by' => $permission->grantedBy ? [
                            'id' => $permission->grantedBy->id,
                            'name' => $permission->grantedBy->name,
                        ] : null,
                        'notes' => $permission->notes,
                    ];
                })->values(),
                'permission_summary' => [
                    'total_products' => $userPermissions->count(),
                    'by_type' => $userPermissions->groupBy('permission_type')->map->count(),
                    'expiring_soon' => $userPermissions->filter(function ($permission) {
                        return $permission->expires_at && $permission->expires_at->diffInDays(now()) <= 7;
                    })->count(),
                ]
            ];
        })->values();
    }
}