<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\Order;
use App\Models\OrderItem;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

/**
 * Daily Sales Overview Service
 *
 * Handles generation of daily sales overview data including:
 * - Today's sales amount and quantity with day-over-day comparison
 * - New customer count
 * - Refund count and rate with day-over-day comparison
 * - Support for product-specific overview data
 */
final class DailySalesOverviewService
{
    /**
     * Generate daily sales overview data
     */
    public function generateOverview(array $filters): array
    {
        $timezone = $filters['timezone'] ?? config('app.timezone');
        $productId = $filters['product_id'] ?? null;
        $organisationIds = $filters['organisation_ids'] ?? [];

        // Get today's date range in the specified timezone
        $today = Carbon::now($timezone)->startOfDay();
        $todayEnd = Carbon::now($timezone)->endOfDay();
        
        // Get yesterday's date range for comparison
        $yesterday = $today->copy()->subDay();
        $yesterdayEnd = $yesterday->copy()->endOfDay();

        // Build base query for today's data
        $todayQuery = $this->buildBaseQuery($today, $todayEnd, $productId, $organisationIds);
        $yesterdayQuery = $this->buildBaseQuery($yesterday, $yesterdayEnd, $productId, $organisationIds);

        // Get today's overview data
        $todayData = $this->getOverviewData($todayQuery, $productId);
        
        // Get yesterday's overview data for comparison
        $yesterdayData = $this->getOverviewData($yesterdayQuery, $productId);

        // Calculate day-over-day comparisons
        $comparisons = $this->calculateComparisons($todayData, $yesterdayData);

        return [
            'today' => $todayData,
            'yesterday' => $yesterdayData,
            'comparisons' => $comparisons,
            'meta' => [
                'date' => $today->toDateString(),
                'timezone' => $timezone,
                'product_id' => $productId,
                'is_product_specific' => !is_null($productId),
            ]
        ];
    }

    /**
     * Build base query for orders within date range
     */
    private function buildBaseQuery(Carbon $startDate, Carbon $endDate, ?int $productId, array $organisationIds): Builder
    {
        $query = Order::query()
            ->whereBetween('completed_at', [$startDate->utc(), $endDate->utc()])
            ->where('state', 'completed');

        // Filter by product if specified
        if ($productId) {
            $query->whereHas('orderItems', function ($q) use ($productId) {
                $q->where('store_variant_id', $productId);
            });
        }

        // Filter by organisation if specified
        if (!empty($organisationIds)) {
            $query->whereHas('orderItems', function ($q) use ($organisationIds) {
                $q->whereIn('store_variant_id', function ($subQuery) use ($organisationIds) {
                    $subQuery->select('products.store_variant_id')
                        ->from('products')
                        ->join('organisations', 'products.owner_id', '=', 'organisations.code')
                        ->whereIn('organisations.id', $organisationIds);
                });
            });
        }

        return $query;
    }

    /**
     * Get overview data from query
     */
    private function getOverviewData(Builder $query, ?int $productId): array
    {
        // Get basic order statistics
        $orderStats = $query->selectRaw('
            COUNT(*) as total_orders,
            SUM(total_amount) as total_sales_amount,
            COUNT(DISTINCT customer_id) as unique_customers,
            SUM(CASE WHEN refund_status = "success" THEN 1 ELSE 0 END) as refunded_orders,
            SUM(CASE WHEN refund_status = "success" THEN refund_total ELSE 0 END) as total_refunds
        ')->first();

        // Get quantity statistics
        $quantityStats = $this->getQuantityStats(clone $query, $productId);

        // Get new customer count
        $newCustomerCount = $this->getNewCustomerCount(clone $query);

        // Calculate refund rate
        $totalOrders = (int) ($orderStats->total_orders ?? 0);
        $refundedOrders = (int) ($orderStats->refunded_orders ?? 0);
        $refundRate = $totalOrders > 0 ? round(($refundedOrders / $totalOrders) * 100, 2) : 0;

        return [
            'sales_amount' => (int) ($orderStats->total_sales_amount ?? 0),
            'sales_amount_formatted' => number_format(($orderStats->total_sales_amount ?? 0) / 100, 2),
            'sales_quantity' => $quantityStats['total_quantity'],
            'new_customers' => $newCustomerCount,
            'refund_count' => $refundedOrders,
            'refund_rate' => $refundRate,
            'total_orders' => $totalOrders,
            'unique_customers' => (int) ($orderStats->unique_customers ?? 0),
            'total_refunds' => (int) ($orderStats->total_refunds ?? 0),
            'total_refunds_formatted' => number_format(($orderStats->total_refunds ?? 0) / 100, 2),
        ];
    }

    /**
     * Get quantity statistics from order items
     */
    private function getQuantityStats(Builder $orderQuery, ?int $productId): array
    {
        // Clone the query and explicitly select the id field
        $orderIds = $orderQuery->select('id')->pluck('id')->toArray();

        if (empty($orderIds)) {
            return ['total_quantity' => 0];
        }

        $itemQuery = OrderItem::whereIn('order_id', $orderIds);

        if ($productId) {
            $itemQuery->where('store_variant_id', $productId);
        }

        $stats = $itemQuery->selectRaw('
            SUM(quantity - quantity_refunded) as total_quantity
        ')->first();

        return [
            'total_quantity' => (int) ($stats->total_quantity ?? 0),
        ];
    }

    /**
     * Get new customer count (customers with their first order today)
     */
    private function getNewCustomerCount(Builder $todayQuery): int
    {
        $todayCustomerIds = $todayQuery->whereNotNull('customer_id')
            ->select('customer_id')
            ->pluck('customer_id')
            ->unique()
            ->toArray();

        if (empty($todayCustomerIds)) {
            return 0;
        }

        // Get today's date in UTC for comparison
        $todayStart = Carbon::now()->startOfDay()->utc();
        $todayEnd = Carbon::now()->endOfDay()->utc();

        // Find customers whose first order was today
        $newCustomers = Order::select('customer_id')
            ->selectRaw('MIN(completed_at) as first_order_date')
            ->whereIn('customer_id', $todayCustomerIds)
            ->whereNotNull('customer_id')
            ->where('state', 'completed')
            ->groupBy('customer_id')
            ->havingRaw('MIN(completed_at) >= ? AND MIN(completed_at) <= ?', [$todayStart, $todayEnd])
            ->count();

        return $newCustomers;
    }

    /**
     * Calculate day-over-day comparisons
     */
    private function calculateComparisons(array $todayData, array $yesterdayData): array
    {
        return [
            'sales_amount' => $this->calculatePercentageChange(
                $todayData['sales_amount'], 
                $yesterdayData['sales_amount']
            ),
            'sales_quantity' => $this->calculatePercentageChange(
                $todayData['sales_quantity'], 
                $yesterdayData['sales_quantity']
            ),
            'refund_count' => $this->calculatePercentageChange(
                $todayData['refund_count'], 
                $yesterdayData['refund_count']
            ),
            'refund_rate' => $this->calculateAbsoluteChange(
                $todayData['refund_rate'], 
                $yesterdayData['refund_rate']
            ),
        ];
    }

    /**
     * Calculate percentage change between two values
     */
    private function calculatePercentageChange(int|float $current, int|float $previous): array
    {
        if ($previous == 0) {
            $percentageChange = $current > 0 ? 100 : 0;
            $trend = $current > 0 ? 'up' : 'neutral';
        } else {
            $percentageChange = round((($current - $previous) / $previous) * 100, 2);
            $trend = $percentageChange > 0 ? 'up' : ($percentageChange < 0 ? 'down' : 'neutral');
        }

        return [
            'value' => $percentageChange,
            'trend' => $trend,
            'formatted' => ($percentageChange >= 0 ? '+' : '') . $percentageChange . '%',
        ];
    }

    /**
     * Calculate absolute change between two values (for rates)
     */
    private function calculateAbsoluteChange(float $current, float $previous): array
    {
        $absoluteChange = round($current - $previous, 2);
        $trend = $absoluteChange > 0 ? 'up' : ($absoluteChange < 0 ? 'down' : 'neutral');

        return [
            'value' => $absoluteChange,
            'trend' => $trend,
            'formatted' => ($absoluteChange >= 0 ? '+' : '') . $absoluteChange . '%',
        ];
    }
}
