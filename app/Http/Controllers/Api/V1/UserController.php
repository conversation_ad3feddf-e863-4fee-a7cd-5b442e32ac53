<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1;

use App\Constants\ErrorCodes;
use App\Http\Requests\Api\V1\CreateUserRequest;
use App\Http\Requests\Api\V1\GuestUserRegistrationRequest;
use App\Http\Requests\Api\V1\UpdateUserRequest;
use App\Http\Resources\Api\V1\CurrentUserResource;
use App\Http\Resources\Api\V1\UserCollection;
use App\Http\Resources\Api\V1\UserResource;
use App\Models\User;
use App\Services\EmailVerificationService;
use App\Services\PermissionService;
use App\Services\UserService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;

final class UserController extends ApiController
{
    public function __construct(
        private readonly UserService $userService,
        private readonly PermissionService $permissionService,
        private readonly EmailVerificationService $emailVerificationService
    ) {
        // Apply authorization to all methods except register and sendVerificationCode
        $this->authorizeResource(User::class, 'user', [
            'except' => ['register']
        ]);
    }

    /**
     * Get current authenticated user information with roles.
     */
    public function getCurrentUser(Request $request): JsonResponse
    {
        $user = $request->user();
        $roleInfo = $this->permissionService->getUserCompleteRoleInfo($user);

        // Load organisations relationship and attach role info
        $user->load('organisations');
        $user->roles = $roleInfo;

        return $this->successResponse(
            new CurrentUserResource($user),
            'User information retrieved successfully'
        );
    }

    /**
     * Display a listing of users.
     */
    public function index(Request $request): JsonResponse
    {
        $perPage = (int) $request->get('per_page', 15);
        $status = $request->get('status');
        $onlyOwner = $request->boolean('only_owner', false);

        // Support for organisation IDs filter (comma-separated string or single organisation_id)
        $organisationIds = null;

        // Handle single organisation_id parameter (convert to array for consistency)
        if ($request->has('organisation_id') && $request->get('organisation_id')) {
            $organisationIds = [(int) $request->get('organisation_id')];
        }

        // Handle multiple organisation_ids parameter (takes priority over single organisation_id)
        if ($request->has('organisation_ids')) {
            $organisationIdsParam = $request->get('organisation_ids');
            if (is_string($organisationIdsParam) && !empty(trim($organisationIdsParam))) {
                // Parse comma-separated string format
                $organisationIds = array_map('intval', array_filter(explode(',', $organisationIdsParam)));
                // Remove empty values and ensure we have valid IDs
                $organisationIds = array_filter($organisationIds, fn($id) => $id > 0);
                $organisationIds = empty($organisationIds) ? null : $organisationIds;
            }
        }

        $users = $this->userService->getUsers($perPage, $status, $request->user(), $organisationIds, $onlyOwner);

        return $this->successResponse(
            new UserCollection($users),
            'api.user.list_retrieved'
        );
    }

    /**
     * Store a newly created user (authenticated admin users only).
     */
    public function store(CreateUserRequest $request): JsonResponse
    {
        $data = $request->validated();
        $user = $this->userService->create($data, $request->user());

        return $this->successResponse(
            new UserResource($user->load('organisations')),
            'api.user.created',
            201
        );
    }

    /**
     * Register a new user (guest registration).
     */
    public function register(GuestUserRegistrationRequest $request): JsonResponse
    {
        $data = $request->validated();
        $user = $this->userService->create($data, null);

        return $this->successResponse(
            new UserResource($user->load('organisations')),
            'api.user.registered',
            201
        );
    }

    /**
     * Send email verification code for guest user registration.
     */
    public function sendVerificationCode(Request $request): JsonResponse
    {
        $request->validate([
            'email' => 'required|email|max:255|unique:users,email',
        ], [
            'email.required' => 'Email address is required',
            'email.email' => 'Email address format is invalid',
            'email.max' => 'Email address cannot exceed 255 characters',
            'email.unique' => 'This email address is already in use',
        ]);

        $email = $request->input('email');

        // Check if we can resend the code (respects retry interval)
        if (!$this->emailVerificationService->canResendCode($email)) {
            return $this->errorResponse(
                'Please wait before requesting a new verification code.',
                null,
                422,
                ErrorCodes::VERIFICATION_CODE_RETRY_LIMIT
            );
        }

        try {
            $code = $this->emailVerificationService->sendVerificationCode($email);

            return $this->successResponse([
                'message' => __('api.user.verification_code_sent_success'),
                'expires_in_seconds' => 900, // 15 minutes - verification code expiry
            ], 'api.user.verification_code_sent');
        } catch (\Exception $e) {
            return $this->errorResponse(
                $e->getMessage(),
                null,
                422,
                ErrorCodes::VERIFICATION_CODE_SEND_ERROR
            );
        }
    }

    /**
     * Display the specified user.
     */
    public function show(Request $request, User $user): JsonResponse
    {
        return $this->successResponse(
            new UserResource($user->load('organisations')),
            'api.user.details_retrieved'
        );
    }

    /**
     * Update the specified user.
     */
    public function update(UpdateUserRequest $request, User $user): JsonResponse
    {
        $data = $request->validated();
        $this->userService->update($user, $data, $request->user());

        return $this->successResponse(
            new UserResource($user->fresh()->load('organisations')),
            'api.user.updated'
        );
    }

    /**
     * Suspend the specified user.
     */
    public function suspend(Request $request, User $user): JsonResponse
    {
        // Check permission for suspend action
        $this->checkPermission('suspend', $user);

        $this->userService->suspend($user, $request->user());

        return $this->successResponse(
            new UserResource($user->fresh()->load('organisations')),
            'api.user.suspended'
        );
    }

    /**
     * Add user to organisation.
     */
    public function addToOrganisation(Request $request, int $userId, int $organisationId): JsonResponse
    {
        $user = $this->userService->getById($userId);

        if (!$user) {
            return $this->notFoundResponse('api.user.not_found');
        }

        // Check permission for adding user to organisation
        $this->authorize('addToOrganisation', [$user, $organisationId]);

        $this->userService->addToOrganisation($user, $organisationId, $request->user());

        return $this->successResponse(
            new UserResource($user->fresh()->load('organisations')),
            'api.user.added_to_organisation'
        );
    }

    /**
     * Remove user from organisation.
     */
    public function removeFromOrganisation(Request $request, int $userId, int $organisationId): JsonResponse
    {
        $user = $this->userService->getById($userId);

        if (!$user) {
            return $this->notFoundResponse('api.user.not_found');
        }

        // Check permission for removing user from organisation
        $this->authorize('removeFromOrganisation', [$user, $organisationId]);

        $this->userService->removeFromOrganisation($user, $organisationId, $request->user());

        return $this->successResponse(
            new UserResource($user->fresh()->load('organisations')),
            'api.user.removed_from_organisation'
        );
    }

    /**
     * Sync user organisations.
     */
    public function syncOrganisations(Request $request, int $userId): JsonResponse
    {
        $user = $this->userService->getById($userId);

        if (!$user) {
            return $this->notFoundResponse('api.user.not_found');
        }

        $organisationIds = $request->input('organisation_ids', []);

        // Check permission for syncing user organisations
        $this->authorize('syncOrganisations', [$user, $organisationIds]);

        $this->userService->syncOrganisations($user, $organisationIds, $request->user());

        return $this->successResponse(
            new UserResource($user->fresh()->load('organisations')),
            'api.user.organisations_synced'
        );
    }

    /**
     * Activate the specified user.
     */
    public function activate(Request $request, User $user): JsonResponse
    {
        // Check permission for activate action
        $this->checkPermission('activate', $user);

        $this->userService->activate($user, $request->user());

        return $this->successResponse(
            new UserResource($user->fresh()->load('organisations')),
            'api.user.activated'
        );
    }

    /**
     * Update current user's profile.
     */
    public function updateProfile(Request $request): JsonResponse
    {
        $request->validate([
            'name' => 'sometimes|string|max:255',
            'email' => 'sometimes|email|max:255|unique:users,email,' . $request->user()->id,
        ]);

        $user = $request->user();
        $data = $request->only(['name', 'email']);

        $this->userService->update($user, $data, $user);

        return $this->successResponse(
            new CurrentUserResource($user->fresh()),
            'api.user.profile_updated'
        );
    }

    /**
     * Change current user's password.
     */
    public function changePassword(Request $request): JsonResponse
    {
        $request->validate([
            'current_password' => 'required|string',
            'password' => 'required|string|min:6|confirmed',
        ]);

        $user = $request->user();

        // Verify current password
        if (!Hash::check($request->current_password, $user->password)) {
            return $this->errorResponse(
                'Current password is incorrect',
                null,
                422,
                'INVALID_CURRENT_PASSWORD'
            );
        }

        // Update password
        $this->userService->update($user, [
            'password' => $request->password
        ], $user);

        return $this->successResponse(
            ['message' => 'Password changed successfully'],
            'api.user.password_changed'
        );
    }

    /**
     * Upload current user's avatar.
     */
    public function uploadAvatar(Request $request): JsonResponse
    {
        $request->validate([
            'avatar' => 'required|image|mimes:jpeg,jpg,png,gif|max:2048', // 2MB max
        ]);

        // TODO: Implement avatar upload logic
        // For now, return a mock response
        return $this->successResponse(
            ['avatar_url' => '/uploads/avatars/default-avatar.png'],
            'api.user.avatar_uploaded'
        );
    }
}
